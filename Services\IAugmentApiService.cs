using System.Collections.Generic;
using System.Threading.Tasks;

namespace AugmentVS2022.Services
{
    /// <summary>
    /// Interface for Augment API service
    /// </summary>
    public interface IAugmentApiService
    {
        /// <summary>
        /// Sends a chat message to Augment and gets a response
        /// </summary>
        /// <param name="message">The user's message</param>
        /// <param name="context">Optional context information</param>
        /// <returns>The AI response</returns>
        Task<AugmentChatResponse> SendChatMessageAsync(string message, AugmentContext context = null);

        /// <summary>
        /// Gets code completions for the given context
        /// </summary>
        /// <param name="request">The completion request</param>
        /// <returns>List of completion suggestions</returns>
        Task<List<AugmentCompletion>> GetCompletionsAsync(AugmentCompletionRequest request);

        /// <summary>
        /// Executes an instruction on the given code
        /// </summary>
        /// <param name="instruction">The natural language instruction</param>
        /// <param name="code">The code to modify</param>
        /// <param name="context">Additional context</param>
        /// <returns>The modified code and explanation</returns>
        Task<AugmentInstructionResponse> ExecuteInstructionAsync(string instruction, string code, AugmentContext context = null);

        /// <summary>
        /// Gets Next Edit suggestions for the given context
        /// </summary>
        /// <param name="request">The Next Edit request</param>
        /// <returns>List of suggested edits</returns>
        Task<AugmentNextEditResponse> GetNextEditSuggestionsAsync(AugmentNextEditRequest request);

        /// <summary>
        /// Starts an Agent task
        /// </summary>
        /// <param name="task">The task description</param>
        /// <param name="context">Project context</param>
        /// <returns>Agent task response</returns>
        Task<AugmentAgentResponse> StartAgentTaskAsync(string task, AugmentContext context = null);
    }

    /// <summary>
    /// Represents context information for API requests
    /// </summary>
    public class AugmentContext
    {
        public string FilePath { get; set; }
        public string Language { get; set; }
        public string ProjectPath { get; set; }
        public int CursorPosition { get; set; }
        public string SelectedText { get; set; }
        public List<string> OpenFiles { get; set; }
        public Dictionary<string, object> Metadata { get; set; }
    }

    /// <summary>
    /// Response from chat API
    /// </summary>
    public class AugmentChatResponse
    {
        public string Message { get; set; }
        public string ConversationId { get; set; }
        public List<AugmentCodeSuggestion> CodeSuggestions { get; set; }
        public bool HasMore { get; set; }
    }

    /// <summary>
    /// Code completion request
    /// </summary>
    public class AugmentCompletionRequest
    {
        public string FilePath { get; set; }
        public string Language { get; set; }
        public string Code { get; set; }
        public int Position { get; set; }
        public AugmentContext Context { get; set; }
    }

    /// <summary>
    /// Code completion suggestion
    /// </summary>
    public class AugmentCompletion
    {
        public string Text { get; set; }
        public string DisplayText { get; set; }
        public string Description { get; set; }
        public int Priority { get; set; }
        public string Kind { get; set; }
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
    }

    /// <summary>
    /// Response from instruction execution
    /// </summary>
    public class AugmentInstructionResponse
    {
        public string ModifiedCode { get; set; }
        public string Explanation { get; set; }
        public List<AugmentEdit> Edits { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }

    /// <summary>
    /// Next Edit request
    /// </summary>
    public class AugmentNextEditRequest
    {
        public string Description { get; set; }
        public AugmentContext Context { get; set; }
        public List<string> AffectedFiles { get; set; }
    }

    /// <summary>
    /// Next Edit response
    /// </summary>
    public class AugmentNextEditResponse
    {
        public List<AugmentEdit> Edits { get; set; }
        public string Explanation { get; set; }
        public List<string> StepsDescription { get; set; }
        public bool RequiresConfirmation { get; set; }
    }

    /// <summary>
    /// Agent task response
    /// </summary>
    public class AugmentAgentResponse
    {
        public string TaskId { get; set; }
        public string Status { get; set; }
        public string Message { get; set; }
        public List<AugmentEdit> ProposedEdits { get; set; }
        public List<string> Steps { get; set; }
        public bool IsComplete { get; set; }
    }

    /// <summary>
    /// Represents a code edit
    /// </summary>
    public class AugmentEdit
    {
        public string FilePath { get; set; }
        public int StartLine { get; set; }
        public int StartColumn { get; set; }
        public int EndLine { get; set; }
        public int EndColumn { get; set; }
        public string OldText { get; set; }
        public string NewText { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Code suggestion from chat
    /// </summary>
    public class AugmentCodeSuggestion
    {
        public string Code { get; set; }
        public string Language { get; set; }
        public string Description { get; set; }
        public string FilePath { get; set; }
    }
}
