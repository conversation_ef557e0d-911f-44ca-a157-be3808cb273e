using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace AugmentVS2022.UI
{
    /// <summary>
    /// Interaction logic for InstructionDialog.xaml
    /// </summary>
    public partial class InstructionDialog : Window, INotifyPropertyChanged
    {
        private string _instruction;

        public string Instruction
        {
            get => _instruction;
            set
            {
                _instruction = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasInstruction));
            }
        }

        public bool HasInstruction => !string.IsNullOrWhiteSpace(Instruction);

        public InstructionDialog()
        {
            InitializeComponent();
            DataContext = this;
            
            // Focus the text box when dialog opens
            Loaded += (s, e) => InstructionTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (HasInstruction)
            {
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
