using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace AugmentVS2022.Services
{
    /// <summary>
    /// Implementation of the Augment completion service
    /// </summary>
    public class AugmentCompletionService : IAugmentCompletionService
    {
        private readonly IAugmentApiService _apiService;
        private bool _isEnabled = true;

        public bool IsEnabled 
        { 
            get => _isEnabled; 
            set => _isEnabled = value; 
        }

        public AugmentCompletionService(IAugmentApiService apiService)
        {
            _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
        }

        public async Task<List<AugmentCompletion>> GetCompletionsAsync(string filePath, string code, int position, string language)
        {
            if (!IsEnabled || _apiService == null)
            {
                return new List<AugmentCompletion>();
            }

            try
            {
                var request = new AugmentCompletionRequest
                {
                    FilePath = filePath,
                    Language = language,
                    Code = code,
                    Position = position,
                    Context = CreateContext(filePath, language, position, code)
                };

                var completions = await _apiService.GetCompletionsAsync(request);
                return completions ?? new List<AugmentCompletion>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Completion service error: {ex.Message}");
                return new List<AugmentCompletion>();
            }
        }

        public async Task<AugmentInlineCompletion> GetInlineCompletionAsync(string filePath, string code, int position, string language)
        {
            if (!IsEnabled || _apiService == null)
            {
                return null;
            }

            try
            {
                // Get regular completions and convert the best one to inline
                var completions = await GetCompletionsAsync(filePath, code, position, language);
                
                if (completions?.Any() == true)
                {
                    var bestCompletion = completions
                        .OrderByDescending(c => c.Priority)
                        .FirstOrDefault();

                    if (bestCompletion != null)
                    {
                        return new AugmentInlineCompletion
                        {
                            Id = Guid.NewGuid().ToString(),
                            Text = bestCompletion.Text,
                            DisplayText = bestCompletion.DisplayText ?? bestCompletion.Text,
                            StartPosition = bestCompletion.StartPosition,
                            EndPosition = bestCompletion.EndPosition,
                            Confidence = Math.Min(bestCompletion.Priority / 100.0, 1.0),
                            Language = language,
                            IsMultiLine = bestCompletion.Text.Contains('\n')
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Inline completion service error: {ex.Message}");
            }

            return null;
        }

        public async Task AcceptInlineCompletionAsync(string completionId)
        {
            // TODO: Send telemetry/feedback to Augment API about accepted completion
            await Task.CompletedTask;
            Debug.WriteLine($"Accepted inline completion: {completionId}");
        }

        public async Task RejectInlineCompletionAsync(string completionId)
        {
            // TODO: Send telemetry/feedback to Augment API about rejected completion
            await Task.CompletedTask;
            Debug.WriteLine($"Rejected inline completion: {completionId}");
        }

        private AugmentContext CreateContext(string filePath, string language, int position, string code)
        {
            // Extract context around the cursor position
            var lines = code.Split('\n');
            var currentLineIndex = 0;
            var currentPosition = 0;

            // Find the current line
            for (int i = 0; i < lines.Length; i++)
            {
                if (currentPosition + lines[i].Length >= position)
                {
                    currentLineIndex = i;
                    break;
                }
                currentPosition += lines[i].Length + 1; // +1 for newline
            }

            // Get surrounding lines for context
            var contextLines = new List<string>();
            var startLine = Math.Max(0, currentLineIndex - 10);
            var endLine = Math.Min(lines.Length - 1, currentLineIndex + 5);

            for (int i = startLine; i <= endLine; i++)
            {
                contextLines.Add(lines[i]);
            }

            return new AugmentContext
            {
                FilePath = filePath,
                Language = language,
                CursorPosition = position,
                Metadata = new Dictionary<string, object>
                {
                    ["currentLineIndex"] = currentLineIndex,
                    ["contextLines"] = contextLines,
                    ["totalLines"] = lines.Length,
                    ["fileSize"] = code.Length
                }
            };
        }
    }
}
