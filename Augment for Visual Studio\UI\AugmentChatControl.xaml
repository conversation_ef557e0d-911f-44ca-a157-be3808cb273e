<UserControl x:Class="Augment_for_Visual_Studio.UI.AugmentChatControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vsshell="clr-namespace:Microsoft.VisualStudio.Shell;assembly=Microsoft.VisualStudio.Shell.15.0"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             Name="AugmentChatControlRoot">
    
    <Grid Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource {x:Static vsshell:VsBrushes.AccentDarkKey}}" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="Augment Chat" 
                          FontWeight="Bold" 
                          Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                          VerticalAlignment="Center"/>
                <Button Name="LoginButton" 
                        Content="Login" 
                        Margin="10,0,0,0" 
                        Padding="8,4"
                        Click="LoginButton_Click"/>
            </StackPanel>
        </Border>
        
        <!-- Chat Messages -->
        <ScrollViewer Grid.Row="1" 
                      Name="ChatScrollViewer"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="5">
            <StackPanel Name="MessagesPanel">
                <!-- Welcome message -->
                <Border Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                        BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Margin="5"
                        Padding="10"
                        HorizontalAlignment="Left"
                        MaxWidth="300">
                    <StackPanel>
                        <TextBlock Text="Augment" 
                                  FontWeight="Bold" 
                                  FontSize="12"
                                  Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                                  Margin="0,0,0,5"/>
                        <TextBlock Text="Welcome to Augment! I'm your AI coding assistant. Please log in to start chatting." 
                                  TextWrapping="Wrap"
                                  Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                BorderThickness="0,1,0,0"
                Padding="10,5">
            <TextBlock Name="StatusText" 
                      Text="Not logged in - Click Login to authenticate"
                      Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                      FontSize="12"/>
        </Border>
        
        <!-- Input Area -->
        <Border Grid.Row="3" 
                Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                BorderThickness="0,1,0,0"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Name="MessageInput" 
                         Grid.Column="0"
                         AcceptsReturn="True"
                         MaxHeight="100"
                         VerticalScrollBarVisibility="Auto"
                         TextWrapping="Wrap"
                         KeyDown="MessageInput_KeyDown"
                         IsEnabled="False"
                         Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                         Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                         BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"/>
                
                <Button Name="SendButton" 
                        Grid.Column="1"
                        Content="Send"
                        Margin="10,0,0,0"
                        Padding="15,8"
                        Click="SendButton_Click"
                        IsEnabled="False"
                        IsDefault="True"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
