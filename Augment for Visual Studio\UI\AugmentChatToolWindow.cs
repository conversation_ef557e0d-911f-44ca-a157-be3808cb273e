using System;
using System.Runtime.InteropServices;
using Microsoft.VisualStudio.Shell;

namespace Augment_for_Visual_Studio.UI
{
    /// <summary>
    /// This class implements the tool window exposed by this package and hosts a user control.
    /// </summary>
    /// <remarks>
    /// In Visual Studio tool windows are composed of a frame (implemented by the shell) and a pane,
    /// usually implemented by the package implementer.
    /// <para>
    /// This class derives from the ToolWindowPane class provided by the Managed Package Framework (MPF)
    /// which provides the implementation of the IVsUIElementPane interface required by the shell.
    /// </para>
    /// </remarks>
    [Guid("7c3a8b2d-4e5f-6a7b-8c9d-0e1f2a3b4c5d")]
    public class AugmentChatToolWindow : ToolWindowPane
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AugmentChatToolWindow"/> class.
        /// </summary>
        public AugmentChatToolWindow() : base(null)
        {
            this.Caption = "Augment Chat";

            // This is the user control hosted by the tool window; Note that, even if this class implements IDisposable,
            // we are not calling Dispose on this object. This is because ToolWindowPane calls Dispose on
            // the object returned by the Content property.
            this.Content = new AugmentChatControl();
        }
    }
}
