using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using AugmentVS2022.Services;
using Microsoft.VisualStudio.Shell;

namespace AugmentVS2022.UI
{
    /// <summary>
    /// Interaction logic for ChatToolWindowControl.xaml
    /// </summary>
    public partial class ChatToolWindowControl : UserControl, INotifyPropertyChanged
    {
        private readonly IAugmentAuthService _authService;
        private readonly IAugmentApiService _apiService;
        
        private ObservableCollection<ChatMessage> _messages;
        private string _currentMessage;
        private string _statusMessage;
        private bool _isLoggedIn;
        private bool _isSending;

        public ObservableCollection<ChatMessage> Messages
        {
            get => _messages;
            set { _messages = value; OnPropertyChanged(); }
        }

        public string CurrentMessage
        {
            get => _currentMessage;
            set { _currentMessage = value; OnPropertyChanged(); OnPropertyChanged(nameof(CanSendMessage)); }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set { _statusMessage = value; OnPropertyChanged(); }
        }

        public bool IsLoggedIn
        {
            get => _isLoggedIn;
            set { _isLoggedIn = value; OnPropertyChanged(); OnPropertyChanged(nameof(CanSendMessage)); }
        }

        public bool CanSendMessage => IsLoggedIn && !string.IsNullOrWhiteSpace(CurrentMessage) && !_isSending;

        public ChatToolWindowControl()
        {
            InitializeComponent();
            DataContext = this;
            
            Messages = new ObservableCollection<ChatMessage>();
            StatusMessage = "Welcome to Augment Chat";
            
            // Get services from package
            ThreadHelper.ThrowIfNotOnUIThread();
            var package = ServiceProvider.GlobalProvider.GetService(typeof(AugmentPackage)) as AugmentPackage;
            _authService = package?.GetService(typeof(IAugmentAuthService)) as IAugmentAuthService;
            _apiService = package?.GetService(typeof(IAugmentApiService)) as IAugmentApiService;
            
            if (_authService != null)
            {
                _authService.AuthenticationChanged += OnAuthenticationChanged;
                IsLoggedIn = _authService.IsAuthenticated;
                UpdateStatusMessage();
            }
            
            // Add welcome message
            if (IsLoggedIn)
            {
                AddMessage("Augment", "Hello! I'm your AI coding assistant. How can I help you today?", false);
            }
            else
            {
                AddMessage("Augment", "Please log in to start chatting with your AI coding assistant.", false);
            }
        }

        private void OnAuthenticationChanged(object sender, AuthenticationChangedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                IsLoggedIn = e.IsAuthenticated;
                UpdateStatusMessage();
                
                if (e.IsAuthenticated)
                {
                    AddMessage("Augment", $"Welcome back, {e.User?.Name ?? "User"}! How can I assist you today?", false);
                }
                else
                {
                    AddMessage("Augment", "You have been logged out. Please log in again to continue.", false);
                }
            });
        }

        private void UpdateStatusMessage()
        {
            if (IsLoggedIn)
            {
                var user = _authService?.CurrentUser;
                StatusMessage = $"Logged in as {user?.Name ?? "User"} ({user?.Plan ?? "Unknown"} plan)";
            }
            else
            {
                StatusMessage = "Not logged in - Click Login to authenticate";
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (_authService == null) return;
            
            StatusMessage = "Logging in...";
            var success = await _authService.LoginAsync();
            
            if (!success)
            {
                StatusMessage = "Login failed. Please try again.";
                AddMessage("System", "Login failed. Please check your credentials and try again.", false);
            }
        }

        private async void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            if (_authService == null) return;
            
            await _authService.LogoutAsync();
        }

        private async void SendButton_Click(object sender, RoutedEventArgs e)
        {
            await SendMessageAsync();
        }

        private async void MessageInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                await SendMessageAsync();
            }
        }

        private async Task SendMessageAsync()
        {
            if (!CanSendMessage || _apiService == null) return;
            
            var message = CurrentMessage.Trim();
            if (string.IsNullOrEmpty(message)) return;
            
            // Add user message
            AddMessage("You", message, true);
            CurrentMessage = string.Empty;
            
            // Show typing indicator
            _isSending = true;
            OnPropertyChanged(nameof(CanSendMessage));
            StatusMessage = "Augment is thinking...";
            
            try
            {
                // Get current context
                var context = await GetCurrentContextAsync();
                
                // Send to API
                var response = await _apiService.SendChatMessageAsync(message, context);
                
                // Add AI response
                if (response != null && !string.IsNullOrEmpty(response.Message))
                {
                    AddMessage("Augment", response.Message, false);
                    
                    // Handle code suggestions if any
                    if (response.CodeSuggestions?.Any() == true)
                    {
                        foreach (var suggestion in response.CodeSuggestions)
                        {
                            AddMessage("Augment", $"Code suggestion for {suggestion.Language}:\n```{suggestion.Language}\n{suggestion.Code}\n```", false);
                        }
                    }
                }
                else
                {
                    AddMessage("Augment", "I'm sorry, I couldn't process your request right now. Please try again.", false);
                }
            }
            catch (Exception ex)
            {
                AddMessage("System", $"Error: {ex.Message}", false);
            }
            finally
            {
                _isSending = false;
                OnPropertyChanged(nameof(CanSendMessage));
                UpdateStatusMessage();
            }
        }

        private async Task<AugmentContext> GetCurrentContextAsync()
        {
            // TODO: Get actual context from Visual Studio
            await Task.CompletedTask;
            
            return new AugmentContext
            {
                FilePath = "current_file.cs", // Placeholder
                Language = "csharp",
                ProjectPath = "current_project",
                CursorPosition = 0,
                OpenFiles = new System.Collections.Generic.List<string>()
            };
        }

        private void AddMessage(string sender, string content, bool isUser)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                Messages.Add(new ChatMessage
                {
                    Sender = sender,
                    Content = content,
                    IsUser = isUser,
                    Timestamp = DateTime.Now
                });
                
                // Scroll to bottom
                ChatScrollViewer.ScrollToEnd();
            });
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// Represents a chat message
    /// </summary>
    public class ChatMessage
    {
        public string Sender { get; set; }
        public string Content { get; set; }
        public bool IsUser { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
