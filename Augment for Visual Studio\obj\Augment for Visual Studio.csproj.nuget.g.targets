﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.visualstudio.threading.analyzers\17.0.64\build\Microsoft.VisualStudio.Threading.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.visualstudio.threading.analyzers\17.0.64\build\Microsoft.VisualStudio.Threading.Analyzers.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.vssdk.compatibilityanalyzer\17.14.2094\build\Microsoft.VsSDK.CompatibilityAnalyzer.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.vssdk.compatibilityanalyzer\17.14.2094\build\Microsoft.VsSDK.CompatibilityAnalyzer.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.visualstudio.sdk.analyzers\17.7.79\build\Microsoft.VisualStudio.SDK.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.visualstudio.sdk.analyzers\17.7.79\build\Microsoft.VisualStudio.SDK.Analyzers.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.vssdk.buildtools\17.14.2094\build\Microsoft.VSSDK.BuildTools.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.vssdk.buildtools\17.14.2094\build\Microsoft.VSSDK.BuildTools.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.visualstudio.setup.configuration.interop\3.0.4496\build\Microsoft.VisualStudio.Setup.Configuration.Interop.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.visualstudio.setup.configuration.interop\3.0.4496\build\Microsoft.VisualStudio.Setup.Configuration.Interop.targets')" />
  </ImportGroup>
</Project>