using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AugmentVS2022.Services;
using Microsoft.VisualStudio.Language.Intellisense;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;

namespace AugmentVS2022.Completions
{
    /// <summary>
    /// Provides AI-powered code completions from Augment
    /// </summary>
    [Export(typeof(ICompletionSourceProvider))]
    [ContentType("CSharp")]
    [ContentType("Basic")]
    [ContentType("C/C++")]
    [ContentType("JavaScript")]
    [ContentType("TypeScript")]
    [ContentType("Python")]
    [Name("AugmentCompletionProvider")]
    internal class AugmentCompletionSourceProvider : ICompletionSourceProvider
    {
        public ICompletionSource TryCreateCompletionSource(ITextBuffer textBuffer)
        {
            return new AugmentCompletionSource(textBuffer);
        }
    }

    /// <summary>
    /// Completion source that provides AI-powered suggestions
    /// </summary>
    internal class AugmentCompletionSource : ICompletionSource
    {
        private readonly ITextBuffer _textBuffer;
        private IAugmentApiService _apiService;
        private bool _disposed = false;

        public AugmentCompletionSource(ITextBuffer textBuffer)
        {
            _textBuffer = textBuffer;
            
            // Get API service from package
            try
            {
                var package = Microsoft.VisualStudio.Shell.ServiceProvider.GlobalProvider.GetService(typeof(AugmentPackage)) as AugmentPackage;
                _apiService = package?.GetService(typeof(IAugmentApiService)) as IAugmentApiService;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to get API service: {ex.Message}");
            }
        }

        public void AugmentCompletionSession(ICompletionSession session, IList<CompletionSet> completionSets)
        {
            if (_disposed || _apiService == null)
                return;

            try
            {
                var snapshot = _textBuffer.CurrentSnapshot;
                var triggerPoint = session.GetTriggerPoint(snapshot);
                
                if (!triggerPoint.HasValue)
                    return;

                var position = triggerPoint.Value.Position;
                var line = triggerPoint.Value.GetContainingLine();
                var lineText = line.GetText();
                var columnIndex = position - line.Start.Position;

                // Get context for the completion request
                var context = GetCompletionContext(snapshot, position);
                var language = GetLanguageFromContentType(_textBuffer.ContentType);

                // Create completion request
                var request = new AugmentCompletionRequest
                {
                    FilePath = GetFilePath(),
                    Language = language,
                    Code = snapshot.GetText(),
                    Position = position,
                    Context = context
                };

                // Get completions asynchronously
                Task.Run(async () =>
                {
                    try
                    {
                        var completions = await _apiService.GetCompletionsAsync(request);
                        
                        if (completions?.Any() == true)
                        {
                            var augmentCompletions = completions.Select(c => CreateCompletion(c, triggerPoint.Value)).ToList();
                            
                            if (augmentCompletions.Any())
                            {
                                var completionSet = new CompletionSet(
                                    "Augment",
                                    "Augment AI Completions",
                                    FindTokenSpanAtPosition(triggerPoint.Value),
                                    augmentCompletions,
                                    null);

                                // Add to completion sets on UI thread
                                Microsoft.VisualStudio.Shell.ThreadHelper.JoinableTaskFactory.Run(async () =>
                                {
                                    await Microsoft.VisualStudio.Shell.ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                                    completionSets.Add(completionSet);
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Completion error: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AugmentCompletionSession error: {ex.Message}");
            }
        }

        private AugmentContext GetCompletionContext(ITextSnapshot snapshot, int position)
        {
            var line = snapshot.GetLineFromPosition(position);
            var lineNumber = line.LineNumber;
            
            // Get surrounding context (previous and next few lines)
            var startLine = Math.Max(0, lineNumber - 5);
            var endLine = Math.Min(snapshot.LineCount - 1, lineNumber + 5);
            
            var contextLines = new List<string>();
            for (int i = startLine; i <= endLine; i++)
            {
                contextLines.Add(snapshot.GetLineFromLineNumber(i).GetText());
            }

            return new AugmentContext
            {
                FilePath = GetFilePath(),
                Language = GetLanguageFromContentType(_textBuffer.ContentType),
                CursorPosition = position,
                Metadata = new Dictionary<string, object>
                {
                    ["lineNumber"] = lineNumber,
                    ["contextLines"] = contextLines,
                    ["currentLine"] = line.GetText()
                }
            };
        }

        private string GetLanguageFromContentType(IContentType contentType)
        {
            if (contentType.IsOfType("CSharp"))
                return "csharp";
            if (contentType.IsOfType("Basic"))
                return "vb";
            if (contentType.IsOfType("C/C++"))
                return "cpp";
            if (contentType.IsOfType("JavaScript"))
                return "javascript";
            if (contentType.IsOfType("TypeScript"))
                return "typescript";
            if (contentType.IsOfType("Python"))
                return "python";
            
            return "text";
        }

        private string GetFilePath()
        {
            // Try to get the file path from the text buffer
            if (_textBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
            {
                return document.FilePath;
            }
            
            return "untitled";
        }

        private Completion CreateCompletion(AugmentCompletion augmentCompletion, SnapshotPoint triggerPoint)
        {
            var displayText = augmentCompletion.DisplayText ?? augmentCompletion.Text;
            var insertionText = augmentCompletion.Text;
            var description = augmentCompletion.Description ?? "AI-powered suggestion";

            return new Completion(
                displayText,
                insertionText,
                description,
                null, // icon
                null  // icon automation text
            );
        }

        private ITrackingSpan FindTokenSpanAtPosition(SnapshotPoint triggerPoint)
        {
            var line = triggerPoint.GetContainingLine();
            var lineText = line.GetText();
            var position = triggerPoint.Position - line.Start.Position;

            // Find the start of the current token
            int start = position;
            while (start > 0 && (char.IsLetterOrDigit(lineText[start - 1]) || lineText[start - 1] == '_'))
            {
                start--;
            }

            // Find the end of the current token
            int end = position;
            while (end < lineText.Length && (char.IsLetterOrDigit(lineText[end]) || lineText[end] == '_'))
            {
                end++;
            }

            var tokenStart = line.Start.Position + start;
            var tokenLength = end - start;

            return triggerPoint.Snapshot.CreateTrackingSpan(tokenStart, tokenLength, SpanTrackingMode.EdgeInclusive);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
    }
}
