using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using AugmentVS2022.Commands;
using AugmentVS2022.UI;
using Microsoft.VisualStudio.Shell;
using Task = System.Threading.Tasks.Task;

namespace AugmentVS2022
{
    /// <summary>
    /// This is the main package class for the Augment Visual Studio 2022 extension.
    /// </summary>
    [PackageRegistration(UseManagedResourcesOnly = true, AllowsBackgroundLoading = true)]
    [Guid(AugmentPackage.PackageGuidString)]
    [ProvideToolWindow(typeof(ChatToolWindow))]
    public sealed class AugmentPackage : AsyncPackage
    {
        /// <summary>
        /// AugmentPackage GUID string.
        /// </summary>
        public const string PackageGuidString = "a7c02a2b-8b4e-4f5d-9e3f-1a2b3c4d5e6f";

        /// <summary>
        /// Initializes a new instance of the <see cref="AugmentPackage"/> class.
        /// </summary>
        public AugmentPackage()
        {
            // Package initialization
        }

        /// <summary>
        /// Initialization of the package; this method is called right after the package is sited.
        /// </summary>
        protected override async Task InitializeAsync(CancellationToken cancellationToken, IProgress<ServiceProgressData> progress)
        {
            // Switch to the UI thread for initialization
            await this.JoinableTaskFactory.SwitchToMainThreadAsync(cancellationToken);

            // Initialize commands
            await ShowChatWindowCommand.InitializeAsync(this);
            await AugmentInstructionsCommand.InitializeAsync(this);
        }
    }
}
