{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Augment for Visual Studio\\Augment for Visual Studio.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Augment for Visual Studio\\Augment for Visual Studio.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Augment for Visual Studio\\Augment for Visual Studio.csproj", "projectName": "Augment for Visual Studio", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Augment for Visual Studio\\Augment for Visual Studio.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Augment for Visual Studio\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.VSSDK.BuildTools": {"target": "Package", "version": "[17.14.2094, )"}, "Microsoft.VisualStudio.SDK": {"target": "Package", "version": "[17.0.32112.339, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}