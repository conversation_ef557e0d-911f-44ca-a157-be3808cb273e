using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace AugmentVS2022.Services
{
    /// <summary>
    /// Implementation of the Augment API service
    /// </summary>
    public class AugmentApiService : IAugmentApiService
    {
        private const string BASE_URL = "https://api.augmentcode.com";
        
        private readonly HttpClient _httpClient;
        private readonly IAugmentAuthService _authService;

        public AugmentApiService(IAugmentAuthService authService)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(BASE_URL);
        }

        public async Task<AugmentChatResponse> SendChatMessageAsync(string message, AugmentContext context = null)
        {
            try
            {
                if (!_authService.IsAuthenticated)
                {
                    throw new UnauthorizedAccessException("User is not authenticated");
                }

                var request = new
                {
                    message = message,
                    context = context
                };

                var response = await SendAuthenticatedRequestAsync<AugmentChatResponse>("POST", "/chat", request);
                return response;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Chat API error: {ex.Message}");
                return new AugmentChatResponse
                {
                    Message = "I'm sorry, I'm having trouble connecting right now. Please try again later.",
                    ConversationId = Guid.NewGuid().ToString()
                };
            }
        }

        public async Task<List<AugmentCompletion>> GetCompletionsAsync(AugmentCompletionRequest request)
        {
            try
            {
                if (!_authService.IsAuthenticated)
                {
                    return new List<AugmentCompletion>();
                }

                var response = await SendAuthenticatedRequestAsync<List<AugmentCompletion>>("POST", "/completions", request);
                return response ?? new List<AugmentCompletion>();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Completions API error: {ex.Message}");
                return new List<AugmentCompletion>();
            }
        }

        public async Task<AugmentInstructionResponse> ExecuteInstructionAsync(string instruction, string code, AugmentContext context = null)
        {
            try
            {
                if (!_authService.IsAuthenticated)
                {
                    throw new UnauthorizedAccessException("User is not authenticated");
                }

                var request = new
                {
                    instruction = instruction,
                    code = code,
                    context = context
                };

                var response = await SendAuthenticatedRequestAsync<AugmentInstructionResponse>("POST", "/instructions", request);
                return response;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Instructions API error: {ex.Message}");
                return new AugmentInstructionResponse
                {
                    Success = false,
                    Error = ex.Message
                };
            }
        }

        public async Task<AugmentNextEditResponse> GetNextEditSuggestionsAsync(AugmentNextEditRequest request)
        {
            try
            {
                if (!_authService.IsAuthenticated)
                {
                    throw new UnauthorizedAccessException("User is not authenticated");
                }

                var response = await SendAuthenticatedRequestAsync<AugmentNextEditResponse>("POST", "/next-edit", request);
                return response;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Next Edit API error: {ex.Message}");
                return new AugmentNextEditResponse
                {
                    Edits = new List<AugmentEdit>(),
                    Explanation = "Unable to generate suggestions at this time."
                };
            }
        }

        public async Task<AugmentAgentResponse> StartAgentTaskAsync(string task, AugmentContext context = null)
        {
            try
            {
                if (!_authService.IsAuthenticated)
                {
                    throw new UnauthorizedAccessException("User is not authenticated");
                }

                var request = new
                {
                    task = task,
                    context = context
                };

                var response = await SendAuthenticatedRequestAsync<AugmentAgentResponse>("POST", "/agent", request);
                return response;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Agent API error: {ex.Message}");
                return new AugmentAgentResponse
                {
                    TaskId = Guid.NewGuid().ToString(),
                    Status = "error",
                    Message = ex.Message,
                    IsComplete = true
                };
            }
        }

        private async Task<T> SendAuthenticatedRequestAsync<T>(string method, string endpoint, object data = null)
        {
            var request = new HttpRequestMessage(new HttpMethod(method), endpoint);
            
            // Add authentication header
            if (!string.IsNullOrEmpty(_authService.AccessToken))
            {
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _authService.AccessToken);
            }

            // Add content if provided
            if (data != null)
            {
                var json = JsonConvert.SerializeObject(data);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }

            var response = await _httpClient.SendAsync(request);
            
            // Handle authentication errors
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                // Try to refresh token
                var refreshed = await _authService.RefreshTokenAsync();
                if (refreshed)
                {
                    // Retry with new token
                    request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _authService.AccessToken);
                    response = await _httpClient.SendAsync(request);
                }
                else
                {
                    throw new UnauthorizedAccessException("Authentication failed");
                }
            }

            response.EnsureSuccessStatusCode();
            
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(responseContent);
        }
    }
}
