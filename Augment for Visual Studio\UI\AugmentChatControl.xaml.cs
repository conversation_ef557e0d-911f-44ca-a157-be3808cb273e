using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Augment_for_Visual_Studio.Services;
using Microsoft.VisualStudio.Shell;

namespace Augment_for_Visual_Studio.UI
{
    /// <summary>
    /// Interaction logic for AugmentChatControl.xaml
    /// </summary>
    public partial class AugmentChatControl : UserControl
    {
        private IAugmentAuthService _authService;
        private IAugmentApiService _apiService;
        private bool _isLoggedIn = false;

        public AugmentChatControl()
        {
            InitializeComponent();
            InitializeServices();
        }

        private void InitializeServices()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                var package = ServiceProvider.GlobalProvider.GetService(typeof(Augment_for_Visual_StudioPackage)) as Augment_for_Visual_StudioPackage;
                _authService = ServiceProvider.GlobalProvider.GetService(typeof(IAugmentAuthService)) as IAugmentAuthService;
                _apiService = ServiceProvider.GlobalProvider.GetService(typeof(IAugmentApiService)) as IAugmentApiService;

                if (_authService != null)
                {
                    _authService.AuthenticationChanged += OnAuthenticationChanged;
                    _isLoggedIn = _authService.IsAuthenticated;
                    UpdateUI();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to initialize services: {ex.Message}");
            }
        }

        private void OnAuthenticationChanged(object sender, AuthenticationChangedEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                _isLoggedIn = e.IsAuthenticated;
                UpdateUI();

                if (e.IsAuthenticated)
                {
                    AddMessage("Augment", $"Welcome back, {e.User?.Name ?? "User"}! How can I assist you today?", false);
                }
                else
                {
                    AddMessage("Augment", "You have been logged out. Please log in again to continue.", false);
                }
            });
        }

        private void UpdateUI()
        {
            LoginButton.Content = _isLoggedIn ? "Logout" : "Login";
            StatusText.Text = _isLoggedIn
                ? $"Logged in as {_authService?.CurrentUser?.Name ?? "User"} ({_authService?.CurrentUser?.Plan ?? "Unknown"} plan)"
                : "Not logged in - Click Login to authenticate";
            MessageInput.IsEnabled = _isLoggedIn;
            SendButton.IsEnabled = _isLoggedIn;
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (_authService == null) return;

            if (!_isLoggedIn)
            {
                StatusText.Text = "Logging in...";
                LoginButton.IsEnabled = false;

                try
                {
                    var success = await _authService.LoginAsync();
                    if (!success)
                    {
                        StatusText.Text = "Login failed. Please try again.";
                        AddMessage("System", "Login failed. Please check your credentials and try again.", false);
                    }
                }
                finally
                {
                    LoginButton.IsEnabled = true;
                }
            }
            else
            {
                await _authService.LogoutAsync();
                MessagesPanel.Children.Clear();
            }
        }

        private void SendButton_Click(object sender, RoutedEventArgs e)
        {
            SendMessage();
        }

        private void MessageInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                SendMessage();
            }
        }

        private async void SendMessage()
        {
            var message = MessageInput.Text.Trim();
            if (string.IsNullOrEmpty(message) || !_isLoggedIn || _apiService == null)
                return;

            // Add user message
            AddMessage("You", message, true);
            MessageInput.Text = string.Empty;

            // Show thinking status
            StatusText.Text = "Augment is thinking...";
            SendButton.IsEnabled = false;

            try
            {
                // Send to API service
                var response = await _apiService.SendChatMessageAsync(message);

                if (response != null && !string.IsNullOrEmpty(response.Message))
                {
                    AddMessage("Augment", response.Message, false);
                }
                else
                {
                    AddMessage("Augment", "I'm sorry, I couldn't process your request right now. Please try again.", false);
                }
            }
            catch (Exception ex)
            {
                AddMessage("System", $"Error: {ex.Message}", false);
            }
            finally
            {
                SendButton.IsEnabled = true;
                UpdateUI();
            }
        }



        private void AddMessage(string sender, string content, bool isUser)
        {
            var border = new Border
            {
                Background = isUser ? 
                    (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.AccentLightKey) : 
                    (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowBackgroundKey),
                BorderBrush = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowBorderKey),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(5),
                Padding = new Thickness(10),
                HorizontalAlignment = isUser ? HorizontalAlignment.Right : HorizontalAlignment.Left,
                MaxWidth = 300
            };

            var stackPanel = new StackPanel();
            
            var senderText = new TextBlock
            {
                Text = sender,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey),
                Margin = new Thickness(0, 0, 0, 5)
            };
            
            var contentText = new TextBlock
            {
                Text = content,
                TextWrapping = TextWrapping.Wrap,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey)
            };
            
            var timeText = new TextBlock
            {
                Text = DateTime.Now.ToString("HH:mm"),
                FontSize = 10,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey),
                Opacity = 0.7,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 5, 0, 0)
            };

            stackPanel.Children.Add(senderText);
            stackPanel.Children.Add(contentText);
            stackPanel.Children.Add(timeText);
            
            border.Child = stackPanel;
            MessagesPanel.Children.Add(border);
            
            // Scroll to bottom
            ChatScrollViewer.ScrollToEnd();
        }
    }
}
