using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace Augment_for_Visual_Studio.UI
{
    /// <summary>
    /// Interaction logic for AugmentChatControl.xaml
    /// </summary>
    public partial class AugmentChatControl : UserControl
    {
        private bool _isLoggedIn = false;

        public AugmentChatControl()
        {
            InitializeComponent();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_isLoggedIn)
            {
                // Simulate login for now
                _isLoggedIn = true;
                LoginButton.Content = "Logout";
                StatusText.Text = "Logged in as Demo User (Professional plan)";
                MessageInput.IsEnabled = true;
                SendButton.IsEnabled = true;
                
                // Add welcome message
                AddMessage("Augment", "Hello! I'm your AI coding assistant. How can I help you today?", false);
            }
            else
            {
                // Logout
                _isLoggedIn = false;
                LoginButton.Content = "Login";
                StatusText.Text = "Not logged in - Click Login to authenticate";
                MessageInput.IsEnabled = false;
                SendButton.IsEnabled = false;
                
                // Clear messages except welcome
                MessagesPanel.Children.Clear();
                AddMessage("Augment", "You have been logged out. Please log in again to continue.", false);
            }
        }

        private void SendButton_Click(object sender, RoutedEventArgs e)
        {
            SendMessage();
        }

        private void MessageInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                SendMessage();
            }
        }

        private void SendMessage()
        {
            var message = MessageInput.Text.Trim();
            if (string.IsNullOrEmpty(message) || !_isLoggedIn)
                return;

            // Add user message
            AddMessage("You", message, true);
            MessageInput.Text = string.Empty;

            // Simulate AI response
            StatusText.Text = "Augment is thinking...";
            
            // Simple demo response
            var response = GetDemoResponse(message);
            AddMessage("Augment", response, false);
            
            StatusText.Text = "Logged in as Demo User (Professional plan)";
        }

        private string GetDemoResponse(string userMessage)
        {
            var lowerMessage = userMessage.ToLower();
            
            if (lowerMessage.Contains("hello") || lowerMessage.Contains("hi"))
            {
                return "Hello! I'm here to help you with your coding tasks. You can ask me to explain code, help with debugging, suggest improvements, or generate new code.";
            }
            else if (lowerMessage.Contains("help"))
            {
                return "I can help you with:\n• Code explanations and documentation\n• Debugging and error fixing\n• Code refactoring and optimization\n• Writing new functions and classes\n• Best practices and design patterns\n\nJust describe what you need help with!";
            }
            else if (lowerMessage.Contains("code") || lowerMessage.Contains("function"))
            {
                return "I'd be happy to help you with code! Could you provide more details about what you're trying to accomplish? For example:\n• What programming language are you using?\n• What functionality do you need?\n• Do you have existing code that needs modification?";
            }
            else
            {
                return "I understand you're asking about: \"" + userMessage + "\"\n\nI'm a demo version right now, but the full Augment extension will provide intelligent responses based on your codebase context and advanced AI capabilities. Stay tuned for the complete implementation!";
            }
        }

        private void AddMessage(string sender, string content, bool isUser)
        {
            var border = new Border
            {
                Background = isUser ? 
                    (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.AccentLightKey) : 
                    (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowBackgroundKey),
                BorderBrush = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowBorderKey),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(5),
                Padding = new Thickness(10),
                HorizontalAlignment = isUser ? HorizontalAlignment.Right : HorizontalAlignment.Left,
                MaxWidth = 300
            };

            var stackPanel = new StackPanel();
            
            var senderText = new TextBlock
            {
                Text = sender,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey),
                Margin = new Thickness(0, 0, 0, 5)
            };
            
            var contentText = new TextBlock
            {
                Text = content,
                TextWrapping = TextWrapping.Wrap,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey)
            };
            
            var timeText = new TextBlock
            {
                Text = DateTime.Now.ToString("HH:mm"),
                FontSize = 10,
                Foreground = (Brush)FindResource(Microsoft.VisualStudio.Shell.VsBrushes.ToolWindowTextKey),
                Opacity = 0.7,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 5, 0, 0)
            };

            stackPanel.Children.Add(senderText);
            stackPanel.Children.Add(contentText);
            stackPanel.Children.Add(timeText);
            
            border.Child = stackPanel;
            MessagesPanel.Children.Add(border);
            
            // Scroll to bottom
            ChatScrollViewer.ScrollToEnd();
        }
    }
}
