{"version": 3, "targets": {".NETFramework,Version=v4.8": {"envdte/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/envdte.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "envdte100/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/envdte100.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "envdte80/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/envdte80.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "envdte90/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/envdte90.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "envdte90a/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/envdte90a.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "MessagePack/2.2.85": {"type": "package", "dependencies": {"MessagePack.Annotations": "2.2.85", "Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Collections.Immutable": "1.5.0", "System.Memory": "4.5.3", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2", "System.Threading.Tasks.Extensions": "4.5.3"}, "compile": {"lib/netstandard2.0/MessagePack.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "MessagePack.Annotations/2.2.85": {"type": "package", "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net461/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "Microsoft.Build.Framework/16.5.0": {"type": "package", "compile": {"lib/net472/Microsoft.Build.Framework.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/_._": {"related": ".pdb;.xml"}}}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.3.2": {"type": "package", "build": {"build/Microsoft.CodeAnalysis.BannedApiAnalyzers.props": {}, "build/Microsoft.CodeAnalysis.BannedApiAnalyzers.targets": {}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.ServiceHub.Analyzers/3.0.3078": {"type": "package"}, "Microsoft.ServiceHub.Client/3.0.3078": {"type": "package", "dependencies": {"Microsoft.ServiceHub.Framework": "3.0.3078", "Microsoft.ServiceHub.Resources": "3.0.3078", "Microsoft.VisualStudio.RemoteControl": "16.3.32", "Microsoft.VisualStudio.Telemetry": "16.3.176", "StreamJsonRpc": "2.7.70", "System.Collections.Immutable": "5.0.0"}, "compile": {"lib/net472/Microsoft.ServiceHub.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.ServiceHub.Framework/3.0.3078": {"type": "package", "dependencies": {"Microsoft.ServiceHub.Analyzers": "3.0.3078", "StreamJsonRpc": "2.7.70", "System.Collections.Immutable": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ServiceHub.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "Microsoft.ServiceHub.Resources/3.0.3078": {"type": "package", "compile": {"lib/net472/Microsoft.ServiceHub.Resources.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.CommandBars/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.CommandBars.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.ComponentModelHost/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.31723.112", "Microsoft.VisualStudio.Interop": "17.0.31723.112", "System.ComponentModel.Composition": "4.5.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.ComponentModelHost.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.CoreUtility/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Threading": "17.0.63", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Composition": "4.5.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.CoreUtility.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Debugger.Interop.10.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.InteropA": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.10.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Debugger.Interop.11.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.InteropA": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.11.0.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Debugger.Interop.12.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.InteropA": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.12.0.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Debugger.Interop.14.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.Interop.11.0": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.14.0.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Debugger.Interop.15.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.Interop.10.0": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.15.0.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Debugger.Interop.16.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Debugger.Interop.10.0": "17.0.32112.339", "Microsoft.VisualStudio.Debugger.Interop.11.0": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.Interop.16.0.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Debugger.InteropA/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Debugger.InteropA.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Designer.Interfaces/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Designer.Interfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Editor/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.GraphModel": "17.0.31723.112", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.31723.112", "Microsoft.VisualStudio.Interop": "17.0.31723.112", "Microsoft.VisualStudio.Language": "17.0.491", "Microsoft.VisualStudio.ProjectAggregator": "17.0.31723.112", "Microsoft.VisualStudio.RpcContracts": "17.0.51", "Microsoft.VisualStudio.Shell.15.0": "17.0.31723.112", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "Microsoft.VisualStudio.Text.UI": "17.0.491", "Microsoft.VisualStudio.Text.UI.Wpf": "17.0.491", "Microsoft.VisualStudio.Threading": "17.0.63", "Microsoft.VisualStudio.Validation": "17.0.28"}, "compile": {"lib/net472/Microsoft.VisualStudio.Editor.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.GraphModel/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339", "System.ComponentModel.Composition": "4.5.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.GraphModel.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.ImageCatalog/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.32112.339", "Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.ImageCatalog.dll": {}}, "runtime": {"lib/net472/_._": {}}}, "Microsoft.VisualStudio.Imaging/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.32112.339", "Microsoft.VisualStudio.Threading": "17.0.64", "Microsoft.VisualStudio.Utilities": "17.0.32112.339", "System.Collections.Immutable": "5.0.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.Imaging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime/17.0.32112.339": {"type": "package", "compile": {"lib/net472/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Interop/17.0.32112.339": {"type": "package", "compile": {"lib/net472/Microsoft.VisualStudio.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Language/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "Microsoft.VisualStudio.Text.UI": "17.0.491", "Newtonsoft.Json": "13.0.1", "StreamJsonRpc": "2.8.28", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Composition": "4.5.0", "System.Private.Uri": "4.3.2"}, "compile": {"lib/net472/Microsoft.VisualStudio.Language.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Language.Intellisense/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.31723.112", "Microsoft.VisualStudio.Language": "17.0.491", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "Microsoft.VisualStudio.Text.UI": "17.0.491", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.Language.Intellisense.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Language.NavigateTo.Interfaces/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Imaging": "17.0.31723.112", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.31723.112", "Microsoft.VisualStudio.Interop": "17.0.31723.112", "Microsoft.VisualStudio.Text.Logic": "17.0.448", "Microsoft.VisualStudio.Utilities": "17.0.31723.112"}, "compile": {"lib/net472/Microsoft.VisualStudio.Language.NavigateTo.Interfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Language.StandardClassification/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Text.Logic": "17.0.491"}, "compile": {"lib/net472/Microsoft.VisualStudio.Language.StandardClassification.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.LanguageServer.Client/17.0.5165": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.448", "Microsoft.VisualStudio.Shell.15.0": "17.0.31723.112", "Microsoft.VisualStudio.Utilities": "17.0.31723.112", "Microsoft.VisualStudio.Validation": "17.0.28", "StreamJsonRpc": "2.8.28"}, "compile": {"lib/net472/Microsoft.VisualStudio.LanguageServer.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.OLE.Interop/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.OLE.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Package.LanguageService.15.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Shell.Framework": "17.0.32112.339"}, "compile": {"lib/net45/Microsoft.VisualStudio.Package.LanguageService.15.0.dll": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.ProjectAggregator/17.0.32112.339": {"type": "package", "compile": {"lib/net472/Microsoft.VisualStudio.ProjectAggregator.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.RemoteControl/16.3.41": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Utilities.Internal": "16.3.23"}, "compile": {"lib/net45/Microsoft.VisualStudio.RemoteControl.dll": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.RpcContracts/17.0.51": {"type": "package", "dependencies": {"Microsoft.ServiceHub.Framework": "3.0.2061", "StreamJsonRpc": "2.8.28"}, "compile": {"lib/netstandard2.0/Microsoft.VisualStudio.RpcContracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.SDK/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CommandBars": "17.0.32112.339", "Microsoft.VisualStudio.ComponentModelHost": "17.0.491", "Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Debugger.Interop.12.0": "17.0.32112.339", "Microsoft.VisualStudio.Debugger.Interop.14.0": "17.0.32112.339", "Microsoft.VisualStudio.Debugger.Interop.15.0": "17.0.32112.339", "Microsoft.VisualStudio.Debugger.Interop.16.0": "17.0.32112.339", "Microsoft.VisualStudio.Designer.Interfaces": "17.0.32112.339", "Microsoft.VisualStudio.Editor": "17.0.491", "Microsoft.VisualStudio.ImageCatalog": "17.0.32112.339", "Microsoft.VisualStudio.Imaging": "17.0.32112.339", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.32112.339", "Microsoft.VisualStudio.Interop": "17.0.32112.339", "Microsoft.VisualStudio.Language": "17.0.491", "Microsoft.VisualStudio.Language.Intellisense": "17.0.491", "Microsoft.VisualStudio.Language.NavigateTo.Interfaces": "17.0.491", "Microsoft.VisualStudio.Language.StandardClassification": "17.0.491", "Microsoft.VisualStudio.LanguageServer.Client": "17.0.5165", "Microsoft.VisualStudio.OLE.Interop": "17.0.32112.339", "Microsoft.VisualStudio.Package.LanguageService.15.0": "17.0.32112.339", "Microsoft.VisualStudio.ProjectAggregator": "17.0.32112.339", "Microsoft.VisualStudio.SDK.Analyzers": "16.10.10", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.0.4496", "Microsoft.VisualStudio.Shell.Design": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop.10.0": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop.11.0": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop.12.0": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop.8.0": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Interop.9.0": "17.0.32112.339", "Microsoft.VisualStudio.TaskRunnerExplorer.14.0": "14.0.0", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "Microsoft.VisualStudio.TextManager.Interop": "17.0.32112.339", "Microsoft.VisualStudio.TextManager.Interop.10.0": "17.0.32112.339", "Microsoft.VisualStudio.TextManager.Interop.11.0": "17.0.32112.339", "Microsoft.VisualStudio.TextManager.Interop.12.0": "17.0.32112.339", "Microsoft.VisualStudio.TextManager.Interop.8.0": "17.0.32112.339", "Microsoft.VisualStudio.TextManager.Interop.9.0": "17.0.32112.339", "Microsoft.VisualStudio.TextTemplating.VSHost": "17.0.32112.339", "Microsoft.VisualStudio.VCProjectEngine": "17.0.32112.339", "Microsoft.VisualStudio.VSHelp": "17.0.32112.339", "Microsoft.VisualStudio.VSHelp80": "17.0.32112.339", "Microsoft.VisualStudio.Validation": "17.0.28", "Microsoft.VisualStudio.WCFReference.Interop": "17.0.32112.339", "Microsoft.VisualStudio.Web.BrowserLink.12.0": "12.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.ComponentModel.Composition": "4.5.0", "VSLangProj": "17.0.32112.339", "VSLangProj100": "17.0.32112.339", "VSLangProj110": "17.0.32112.339", "VSLangProj140": "17.0.32112.339", "VSLangProj150": "17.0.32112.339", "VSLangProj157": "17.0.32112.339", "VSLangProj158": "17.0.32112.339", "VSLangProj165": "17.0.32112.339", "VSLangProj2": "17.0.32112.339", "VSLangProj80": "17.0.32112.339", "VSLangProj90": "17.0.32112.339", "envdte": "17.0.32112.339", "envdte100": "17.0.32112.339", "envdte80": "17.0.32112.339", "envdte90": "17.0.32112.339", "envdte90a": "17.0.32112.339", "stdole": "17.0.32112.339"}}, "Microsoft.VisualStudio.SDK.Analyzers/16.10.10": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.BannedApiAnalyzers": "3.3.2", "Microsoft.VisualStudio.Threading.Analyzers": "16.10.56"}, "build": {"build/Microsoft.VisualStudio.SDK.Analyzers.targets": {}}}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.0.4496": {"type": "package", "compile": {"lib/net35/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net35/_._": {"related": ".xml"}}, "build": {"build/Microsoft.VisualStudio.Setup.Configuration.Interop.targets": {}}}, "Microsoft.VisualStudio.Shell.15.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.ComponentModelHost": "17.0.491", "Microsoft.VisualStudio.ImageCatalog": "17.0.32112.339", "Microsoft.VisualStudio.Imaging": "17.0.32112.339", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.32112.339", "Microsoft.VisualStudio.Interop": "17.0.32112.339", "Microsoft.VisualStudio.ProjectAggregator": "17.0.32112.339", "Microsoft.VisualStudio.SDK.Analyzers": "16.10.10", "Microsoft.VisualStudio.Shell.Framework": "17.0.32112.339", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Utilities": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.15.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Design/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.ImageCatalog": "17.0.32112.339", "Microsoft.VisualStudio.Interop": "17.0.32112.339", "Microsoft.VisualStudio.Shell.15.0": "17.0.32112.339", "Microsoft.VisualStudio.Shell.Framework": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Design.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Framework/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "16.5.0", "Microsoft.VisualStudio.GraphModel": "17.0.32112.339", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.32112.339", "Microsoft.VisualStudio.Interop": "17.0.32112.339", "Microsoft.VisualStudio.SDK.Analyzers": "16.10.10", "Microsoft.VisualStudio.Telemetry": "16.3.250", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Threading": "17.0.64", "Microsoft.VisualStudio.Utilities": "17.0.32112.339", "Newtonsoft.Json": "13.0.1", "System.ComponentModel.Composition": "4.5.0", "System.Threading.Tasks.Dataflow": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop.10.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.10.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop.11.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.11.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop.12.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.12.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop.8.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.8.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Shell.Interop.9.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.Shell.Interop.9.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TaskRunnerExplorer.14.0/14.0.0": {"type": "package", "compile": {"lib/net40/Microsoft.VisualStudio.TaskRunnerExplorer.14.0.dll": {}}, "runtime": {"lib/net40/_._": {}}}, "Microsoft.VisualStudio.Telemetry/16.3.250": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.VisualStudio.RemoteControl": "16.3.41", "Microsoft.VisualStudio.Utilities.Internal": "16.3.23", "Newtonsoft.Json": "9.0.1"}, "compile": {"lib/net45/Microsoft.VisualStudio.Telemetry.dll": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Text.Data/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Threading": "17.0.63"}, "compile": {"lib/net472/Microsoft.VisualStudio.Text.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Text.Logic/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Text.Data": "17.0.491", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Composition": "4.5.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.Text.Logic.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Text.UI/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "System.ComponentModel.Composition": "4.5.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.Text.UI.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Text.UI.Wpf/17.0.491": {"type": "package", "dependencies": {"Microsoft.VisualStudio.CoreUtility": "17.0.491", "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime": "17.0.31723.112", "Microsoft.VisualStudio.Text.Data": "17.0.491", "Microsoft.VisualStudio.Text.Logic": "17.0.491", "Microsoft.VisualStudio.Text.UI": "17.0.491"}, "compile": {"lib/net472/Microsoft.VisualStudio.Text.UI.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop.10.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.10.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop.11.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.11.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop.12.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.12.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop.8.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.8.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextManager.Interop.9.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextManager.Interop.9.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextTemplating/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.TextTemplating.Interfaces": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextTemplating.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextTemplating.Interfaces/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.TextTemplating.Interfaces.11.0": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextTemplating.Interfaces.10.0/17.0.32112.339": {"type": "package", "compile": {"lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextTemplating.Interfaces.11.0/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.TextTemplating.Interfaces.10.0": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.TextTemplating.VSHost/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Shell.Framework": "17.0.32112.339", "Microsoft.VisualStudio.TextTemplating": "17.0.32112.339", "Microsoft.VisualStudio.Validation": "17.0.28", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.TextTemplating.VSHost.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Threading/17.0.64": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.VisualStudio.Threading.Analyzers": "17.0.64", "Microsoft.VisualStudio.Validation": "16.10.35", "Microsoft.Win32.Registry": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net472/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Threading.Analyzers/17.0.64": {"type": "package", "build": {"build/Microsoft.VisualStudio.Threading.Analyzers.targets": {}}}, "Microsoft.VisualStudio.Utilities/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.ServiceHub.Client": "3.0.3078", "Microsoft.VisualStudio.RpcContracts": "17.0.51", "Microsoft.VisualStudio.Telemetry": "16.3.250", "System.ComponentModel.Composition": "4.5.0", "System.Threading.AccessControl": "5.0.0", "System.Threading.Tasks.Dataflow": "5.0.0"}, "compile": {"lib/net472/Microsoft.VisualStudio.Utilities.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Utilities.Internal/16.3.23": {"type": "package", "compile": {"lib/net45/_._": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Validation/17.0.28": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.VCProjectEngine/17.0.32112.339": {"type": "package", "compile": {"lib/net45/Microsoft.VisualStudio.VCProjectEngine.dll": {}}, "runtime": {"lib/net45/_._": {}}}, "Microsoft.VisualStudio.VSHelp/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.VSHelp.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.VSHelp80/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.VSHelp80.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.WCFReference.Interop/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/Microsoft.VisualStudio.WCFReference.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "Microsoft.VisualStudio.Web.BrowserLink.12.0/12.0.0": {"type": "package", "compile": {"lib/net40/Microsoft.VisualStudio.Web.BrowserLink.12.0.dll": {"related": ".xml"}}, "runtime": {"lib/net40/_._": {"related": ".xml"}}}, "Microsoft.VSSDK.BuildTools/17.9.3168": {"type": "package", "dependencies": {"Microsoft.VisualStudio.SDK.Analyzers": "16.10.10", "Microsoft.VsSDK.CompatibilityAnalyzer": "17.9.3168"}, "frameworkAssemblies": ["Microsoft.Win32.Primitives"], "build": {"build/Microsoft.VSSDK.BuildTools.props": {}, "build/Microsoft.VSSDK.BuildTools.targets": {}}}, "Microsoft.VsSDK.CompatibilityAnalyzer/17.9.3168": {"type": "package", "frameworkAssemblies": ["System.IO.Compression", "System.IO.Compression.FileSystem", "WindowsBase"], "build": {"build/Microsoft.VsSDK.CompatibilityAnalyzer.props": {}, "build/Microsoft.VsSDK.CompatibilityAnalyzer.targets": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "compile": {"ref/net46/Microsoft.Win32.Primitives.dll": {}}, "runtime": {"lib/net46/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/net461/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/_._": {"assetType": "runtime", "rid": "win"}}}, "Nerdbank.Streams/2.6.81": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "Microsoft.VisualStudio.Threading": "16.7.56", "Microsoft.VisualStudio.Validation": "15.5.31", "System.IO.Pipelines": "4.7.2", "System.Net.WebSockets": "4.3.0", "System.Runtime.CompilerServices.Unsafe": "4.7.1"}, "compile": {"lib/netstandard2.0/Nerdbank.Streams.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".pdb;.xml"}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "stdole/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/stdole.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "StreamJsonRpc/2.8.28": {"type": "package", "dependencies": {"MessagePack": "2.2.85", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.VisualStudio.Threading": "16.9.60", "Nerdbank.Streams": "2.6.81", "Newtonsoft.Json": "12.0.2", "System.Collections.Immutable": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.IO.Pipelines": "5.0.1", "System.Memory": "4.5.4", "System.Net.Http": "4.3.4", "System.Net.WebSockets": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Threading.Tasks.Dataflow": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/StreamJsonRpc.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/_._": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "System.Collections.Immutable/5.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/net461/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "System.ComponentModel.Composition/4.5.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "compile": {"lib/net46/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net46/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "compile": {"ref/net462/System.IO.dll": {}}, "runtime": {"lib/net462/_._": {}}}, "System.IO.Pipelines/5.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net461/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "System.Net.Http/4.3.4": {"type": "package", "dependencies": {"System.Security.Cryptography.X509Certificates": "4.3.0"}, "compile": {"ref/net46/System.Net.Http.dll": {}}, "runtime": {"lib/net46/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net46/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Net.WebSockets/4.3.0": {"type": "package", "compile": {"ref/net46/System.Net.WebSockets.dll": {}}, "runtime": {"lib/net46/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/_._": {"related": ".xml"}}}, "System.Private.Uri/4.3.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}, "compile": {"ref/netstandard/_._": {}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reflection.Emit.Lightweight/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "compile": {"ref/net462/System.Runtime.dll": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net45/_._": {"related": ".xml"}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}, "compile": {"ref/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtime": {"lib/net463/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net463/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "compile": {"ref/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtime": {"lib/net46/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net46/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "compile": {"ref/net46/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/net46/_._": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0"}, "compile": {"ref/net461/System.Security.Cryptography.X509Certificates.dll": {}}, "runtime": {"lib/net461/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net461/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/net461/System.Threading.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks.Dataflow/5.0.0": {"type": "package", "compile": {"lib/net461/System.Threading.Tasks.Dataflow.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/_._": {"related": ".xml"}}}, "VSLangProj/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj100/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj100.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj110/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj110.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj140/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj140.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj150/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj150.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj157/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj157.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj158/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj158.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj165/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj165.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj2/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj2.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj80/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj80.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}, "VSLangProj90/17.0.32112.339": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Interop": "17.0.32112.339"}, "compile": {"lib/net472/VSLangProj90.dll": {"related": ".xml"}}, "runtime": {"lib/net472/_._": {"related": ".xml"}}}}}, "libraries": {"envdte/17.0.32112.339": {"sha512": "vegjmu5MMQJi8Z7GxFOaRAwI54g+Eh4/QnfwzbmT1olqmqUIPiknLOxx0ciV936gXfJIAlqCyk7KX+aM1GSElg==", "type": "package", "path": "envdte/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "envdte.17.0.32112.339.nupkg.sha512", "envdte.nuspec", "lib/net20/envdte.dll", "lib/net20/envdte.xml", "lib/net45/envdte.dll", "lib/net45/envdte.xml", "lib/net472/envdte.dll", "lib/net472/envdte.xml", "lib/netcoreapp3.1/envdte.dll", "lib/netcoreapp3.1/envdte.xml", "lib/netstandard2.0/envdte.dll", "lib/netstandard2.0/envdte.xml"]}, "envdte100/17.0.32112.339": {"sha512": "6Oynd5SNiRTlJD5cOBDqIg1sfTk76DMrTSQ9i2UiSN2KS14mcqHrMGogGnI+L1KOri9CiarSoU/CWZP3Vu8G/g==", "type": "package", "path": "envdte100/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "envdte100.17.0.32112.339.nupkg.sha512", "envdte100.nuspec", "lib/net20/envdte100.dll", "lib/net20/envdte100.xml", "lib/net45/envdte100.dll", "lib/net45/envdte100.xml", "lib/net472/envdte100.dll", "lib/net472/envdte100.xml", "lib/netcoreapp3.1/envdte100.dll", "lib/netcoreapp3.1/envdte100.xml", "lib/netstandard2.0/envdte100.dll", "lib/netstandard2.0/envdte100.xml"]}, "envdte80/17.0.32112.339": {"sha512": "ww0cHlTSkJSGehp0rfbntNwr6muf6DfkHZwruW4Ju9E7jVHiwZPxoyRpDoK9nDtrYyJBCZfiO5RZL03+g8ao3w==", "type": "package", "path": "envdte80/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "envdte80.17.0.32112.339.nupkg.sha512", "envdte80.nuspec", "lib/net20/envdte80.dll", "lib/net20/envdte80.xml", "lib/net45/envdte80.dll", "lib/net45/envdte80.xml", "lib/net472/envdte80.dll", "lib/net472/envdte80.xml", "lib/netcoreapp3.1/envdte80.dll", "lib/netcoreapp3.1/envdte80.xml", "lib/netstandard2.0/envdte80.dll", "lib/netstandard2.0/envdte80.xml"]}, "envdte90/17.0.32112.339": {"sha512": "Hh0aDSfgzBPds7VyinPQZ4o3/86GVsmf1HfG9sXf1O/A90wf3zbeZvDPuGFl20u/8MaVBUemgn8KI/zGw6b/NA==", "type": "package", "path": "envdte90/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "envdte90.17.0.32112.339.nupkg.sha512", "envdte90.nuspec", "lib/net20/envdte90.dll", "lib/net20/envdte90.xml", "lib/net45/envdte90.dll", "lib/net45/envdte90.xml", "lib/net472/envdte90.dll", "lib/net472/envdte90.xml", "lib/netcoreapp3.1/envdte90.dll", "lib/netcoreapp3.1/envdte90.xml", "lib/netstandard2.0/envdte90.dll", "lib/netstandard2.0/envdte90.xml"]}, "envdte90a/17.0.32112.339": {"sha512": "Tb1xT8teFjZVd8vvEooBICgmNaPOdMnm80q7eDMF2K1tNbRmhqAHyNV3LKPHwjcZi2RZKGPfDLN7sdpKkffMsQ==", "type": "package", "path": "envdte90a/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "envdte90a.17.0.32112.339.nupkg.sha512", "envdte90a.nuspec", "lib/net20/envdte90a.dll", "lib/net20/envdte90a.xml", "lib/net45/envdte90a.dll", "lib/net45/envdte90a.xml", "lib/net472/envdte90a.dll", "lib/net472/envdte90a.xml", "lib/netcoreapp3.1/envdte90a.dll", "lib/netcoreapp3.1/envdte90a.xml", "lib/netstandard2.0/envdte90a.dll", "lib/netstandard2.0/envdte90a.xml"]}, "MessagePack/2.2.85": {"sha512": "3SqAgwNV5LOf+ZapHmjQMUc7WDy/1ur9CfFNjgnfMZKCB5CxkVVbyHa06fObjGTEHZI7mcDathYjkI+ncr92ZQ==", "type": "package", "path": "messagepack/2.2.85", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/netcoreapp2.1/MessagePack.dll", "lib/netcoreapp2.1/MessagePack.xml", "lib/netstandard2.0/MessagePack.dll", "lib/netstandard2.0/MessagePack.xml", "messagepack.2.2.85.nupkg.sha512", "messagepack.nuspec"]}, "MessagePack.Annotations/2.2.85": {"sha512": "YptRsDCQK35K5FhmZ0LojW4t8I6DpetLfK5KG8PVY2f6h7/gdyr8f4++xdSEK/xS6XX7/GPvEpqszKVPksCsiQ==", "type": "package", "path": "messagepack.annotations/2.2.85", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/netstandard2.0/MessagePack.Annotations.dll", "lib/netstandard2.0/MessagePack.Annotations.xml", "messagepack.annotations.2.2.85.nupkg.sha512", "messagepack.annotations.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"sha512": "W8DPQjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Build.Framework/16.5.0": {"sha512": "K0hfdWy+0p8DJXxzpNc4T5zHm4hf9QONAvyzvw3utKExmxRBShtV/+uHVYTblZWk+rIHNEHeglyXMmqfSshdFA==", "type": "package", "path": "microsoft.build.framework/16.5.0", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/netstandard2.0/Microsoft.Build.Framework.dll", "lib/netstandard2.0/Microsoft.Build.Framework.pdb", "lib/netstandard2.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.16.5.0.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt"]}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.3.2": {"sha512": "LlcsDRSYfkJFWOdDpysY/4Ph4llHc8DLOc3roFTz9+216vl+vwVGfbys2rcSmhZCTDv/0kxSs2oOdd9SX2NiVg==", "type": "package", "path": "microsoft.codeanalysis.bannedapianalyzers/3.3.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.BannedApiAnalyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.BannedApiAnalyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.BannedApiAnalyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.BannedApiAnalyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.BannedApiAnalyzers.resources.dll", "build/Microsoft.CodeAnalysis.BannedApiAnalyzers.props", "build/Microsoft.CodeAnalysis.BannedApiAnalyzers.targets", "build/config/AnalysisLevel_2_9_8_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_Default.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.BannedApiAnalyzers.md", "documentation/Microsoft.CodeAnalysis.BannedApiAnalyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/ApiDesignRulesDefault/.editorconfig", "editorconfig/ApiDesignRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.bannedapianalyzers.3.3.2.nupkg.sha512", "microsoft.codeanalysis.bannedapianalyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/ApiDesignRulesDefault.ruleset", "rulesets/ApiDesignRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Platforms/1.1.1": {"sha512": "TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "type": "package", "path": "microsoft.netcore.platforms/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.1.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.3": {"sha512": "3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "type": "package", "path": "microsoft.netcore.targets/1.1.3", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.3.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.ServiceHub.Analyzers/3.0.3078": {"sha512": "LQsmEP/5i9PvM6O1dx69Yj3C0z/tSWiaLjoX31jQ+ilJZ8x7yqthYOnWaQpeZKxJn+oFxymzGtXgPasnqYM/ww==", "type": "package", "path": "microsoft.servicehub.analyzers/3.0.3078", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "analyzers/Microsoft.ServiceHub.Analyzers.dll", "analyzers/cs/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/de/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/es/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/fr/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/it/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/ja/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/ko/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/pl/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/pt-BR/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/ru/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/tr/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/zh-Hans/Microsoft.ServiceHub.Analyzers.resources.dll", "analyzers/zh-Hant/Microsoft.ServiceHub.Analyzers.resources.dll", "microsoft.servicehub.analyzers.3.0.3078.nupkg.sha512", "microsoft.servicehub.analyzers.nuspec"]}, "Microsoft.ServiceHub.Client/3.0.3078": {"sha512": "hYqQlgUhnTq7VHYfIBvuWCwAiTjqhCfEX7d/ISVtEGEv7/N89QAbL+0XCz2NZRN6yMDtVMEoee5Q4k6/uwWlJg==", "type": "package", "path": "microsoft.servicehub.client/3.0.3078", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.ServiceHub.Client.dll", "lib/net472/Microsoft.ServiceHub.Client.xml", "lib/netstandard2.0/Microsoft.ServiceHub.Client.dll", "lib/netstandard2.0/Microsoft.ServiceHub.Client.xml", "microsoft.servicehub.client.3.0.3078.nupkg.sha512", "microsoft.servicehub.client.nuspec"]}, "Microsoft.ServiceHub.Framework/3.0.3078": {"sha512": "RMBx+TEE3Fl6CRd1d1ZWKnNPRbPL23NFydDEEjRtZdwTSWe1x0gkUqnGU/ZgtqSsgWUfaQtEPxd8S9qfPGkz0Q==", "type": "package", "path": "microsoft.servicehub.framework/3.0.3078", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/netstandard2.0/Microsoft.ServiceHub.Framework.dll", "lib/netstandard2.0/Microsoft.ServiceHub.Framework.xml", "lib/netstandard2.0/cs/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/de/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/es/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/fr/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/it/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/ja/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/ko/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/pl/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/ru/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/tr/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.ServiceHub.Framework.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.ServiceHub.Framework.resources.dll", "microsoft.servicehub.framework.3.0.3078.nupkg.sha512", "microsoft.servicehub.framework.nuspec"]}, "Microsoft.ServiceHub.Resources/3.0.3078": {"sha512": "02mGIKyVfnXFEeicpV2RbZapHd6vcefFSSZvjAA+O0kWgB9x2D5Pd3M94Il9LiLgFnw3mmxtf68tbEjOhQ0rWg==", "type": "package", "path": "microsoft.servicehub.resources/3.0.3078", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "lib/net472/Microsoft.ServiceHub.Resources.dll", "lib/net472/Microsoft.ServiceHub.Resources.xml", "lib/net472/cs/Microsoft.ServiceHub.Resources.dll", "lib/net472/de/Microsoft.ServiceHub.Resources.dll", "lib/net472/es/Microsoft.ServiceHub.Resources.dll", "lib/net472/fr/Microsoft.ServiceHub.Resources.dll", "lib/net472/it/Microsoft.ServiceHub.Resources.dll", "lib/net472/ja/Microsoft.ServiceHub.Resources.dll", "lib/net472/ko/Microsoft.ServiceHub.Resources.dll", "lib/net472/pl/Microsoft.ServiceHub.Resources.dll", "lib/net472/pt-BR/Microsoft.ServiceHub.Resources.dll", "lib/net472/ru/Microsoft.ServiceHub.Resources.dll", "lib/net472/tr/Microsoft.ServiceHub.Resources.dll", "lib/net472/zh-<PERSON>/Microsoft.ServiceHub.Resources.dll", "lib/net472/zh-Hant/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/Microsoft.ServiceHub.Resources.xml", "lib/netstandard2.0/cs/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/de/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/es/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/fr/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/it/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/ja/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/ko/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/pl/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/ru/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/tr/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.ServiceHub.Resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.ServiceHub.Resources.dll", "microsoft.servicehub.resources.3.0.3078.nupkg.sha512", "microsoft.servicehub.resources.nuspec"]}, "Microsoft.VisualStudio.CommandBars/17.0.32112.339": {"sha512": "XrcY4kjQyio29YJBHMoiPd23SchPhfCn2HMkVz3niZFjcohcyGXYCzjNHk+yMe3koggtLCk1EuWY6nPTKZrlcQ==", "type": "package", "path": "microsoft.visualstudio.commandbars/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.CommandBars.dll", "lib/net20/Microsoft.VisualStudio.CommandBars.xml", "lib/net45/Microsoft.VisualStudio.CommandBars.dll", "lib/net45/Microsoft.VisualStudio.CommandBars.xml", "lib/net472/Microsoft.VisualStudio.CommandBars.dll", "lib/net472/Microsoft.VisualStudio.CommandBars.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.CommandBars.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.CommandBars.xml", "lib/netstandard2.0/Microsoft.VisualStudio.CommandBars.dll", "lib/netstandard2.0/Microsoft.VisualStudio.CommandBars.xml", "microsoft.visualstudio.commandbars.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.commandbars.nuspec"]}, "Microsoft.VisualStudio.ComponentModelHost/17.0.491": {"sha512": "zQUJ+6w6agjjcyNHUhgxVdXpnVaf3ge7EM4JxgydYIDQvxKt64SZwgPxegbiqSPIsauzVl+U1ovniAQbmQdARw==", "type": "package", "path": "microsoft.visualstudio.componentmodelhost/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.ComponentModelHost.dll", "lib/net472/Microsoft.VisualStudio.ComponentModelHost.xml", "lib/netstandard2.0/Microsoft.VisualStudio.ComponentModelHost.dll", "lib/netstandard2.0/Microsoft.VisualStudio.ComponentModelHost.xml", "microsoft.visualstudio.componentmodelhost.17.0.491.nupkg.sha512", "microsoft.visualstudio.componentmodelhost.nuspec"]}, "Microsoft.VisualStudio.CoreUtility/17.0.491": {"sha512": "Cbnh+jk/fYNEvLwNRgZ2pA0/+bR2QxU+nZx7+RYPTBegO4f2h8w17bqaDmRAiEQxdjMB3AfXabUILBDHJf6Yqg==", "type": "package", "path": "microsoft.visualstudio.coreutility/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.CoreUtility.dll", "lib/net472/Microsoft.VisualStudio.CoreUtility.xml", "lib/netstandard2.0/Microsoft.VisualStudio.CoreUtility.dll", "lib/netstandard2.0/Microsoft.VisualStudio.CoreUtility.xml", "microsoft.visualstudio.coreutility.17.0.491.nupkg.sha512", "microsoft.visualstudio.coreutility.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.10.0/17.0.32112.339": {"sha512": "SsNIZS/QMiSjkIWIhFf7dkXLpfRTXA4D5SVE37Fi7wqQxR4CHKEorjqAMg9tui5MdRL5AoDjVIc4rD9o1PTwTA==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.10.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.10.0.dll", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.10.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.10.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.10.0.xml", "microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.10.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.11.0/17.0.32112.339": {"sha512": "nkZyo9cuWX/cz/plvhYe4W6IU0dpELL4yb5SrXv2U1xfWO13W4mEqDg6Ph+q7ockFz+rs6Ap5OtydvcRr2/NXg==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.11.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.11.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.11.0.dll", "microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.11.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.12.0/17.0.32112.339": {"sha512": "tUanRFKq3d9nY9xc1f5FmphicizaJbtjVivVnI+Zd3vEg8BXNY1KV3CIYoTq9ZN6JAXjIsziLWqDiRGhxI004Q==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.12.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.12.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.12.0.dll", "microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.12.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.14.0/17.0.32112.339": {"sha512": "56EX8PKwVy9pOsDkhNxNNNr5ZhNB/V6a5y02bkKUKhbpra9Z9Amw4Q0M7sas5dQsA2uTTRohNDVGVxYSt0BtLw==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.14.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.14.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.14.0.dll", "microsoft.visualstudio.debugger.interop.14.0.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.14.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.15.0/17.0.32112.339": {"sha512": "x9pPbGnf9ZuImSiEDXIxKwDWWkThif1lrwafhyTuc9qQTCc6p40CimmDSt438SaPaksEbe5sB+yk9Fp/iNLDPg==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.15.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.15.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.15.0.dll", "microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.15.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.Interop.16.0/17.0.32112.339": {"sha512": "1egZBx8+/3xhLH+4Syw2AWsNdUPbivDVolhl85cTLc9C3vUf0RCBZSiF38EPKNiDpH1WSsCjVcf00MuEelJEOA==", "type": "package", "path": "microsoft.visualstudio.debugger.interop.16.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.Interop.16.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Interop.16.0.dll", "microsoft.visualstudio.debugger.interop.16.0.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interop.16.0.nuspec"]}, "Microsoft.VisualStudio.Debugger.InteropA/17.0.32112.339": {"sha512": "isRz03bS7AFl36BKqgl/gs/GMZ8RCW/89WrfAHolcfz0bShvkGylrdS7VK0TUDUZtl22GFMsc88q3fd7MBy1nA==", "type": "package", "path": "microsoft.visualstudio.debugger.interopa/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Debugger.InteropA.dll", "lib/net472/Microsoft.VisualStudio.Debugger.InteropA.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.InteropA.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Debugger.InteropA.xml", "microsoft.visualstudio.debugger.interopa.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.debugger.interopa.nuspec"]}, "Microsoft.VisualStudio.Designer.Interfaces/17.0.32112.339": {"sha512": "7Xox0RTUy6Hd0V5iTpCulkwU4tGph25P1z8ydk7eb3xxCFaModkEozlsaFYStG1InJFIMFFTbdQ51/C/y6+VDw==", "type": "package", "path": "microsoft.visualstudio.designer.interfaces/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Designer.Interfaces.dll", "lib/net20/Microsoft.VisualStudio.Designer.Interfaces.xml", "lib/net45/Microsoft.VisualStudio.Designer.Interfaces.dll", "lib/net45/Microsoft.VisualStudio.Designer.Interfaces.xml", "lib/net472/Microsoft.VisualStudio.Designer.Interfaces.dll", "lib/net472/Microsoft.VisualStudio.Designer.Interfaces.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Designer.Interfaces.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Designer.Interfaces.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Designer.Interfaces.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Designer.Interfaces.xml", "microsoft.visualstudio.designer.interfaces.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.designer.interfaces.nuspec"]}, "Microsoft.VisualStudio.Editor/17.0.491": {"sha512": "FyXG/2silaszHliolxRZqR+Sr2kveLCk7Vq8uwo+ThQ2xsGzQvr+I7RAyxQ11EiXOgKpKGqsD+IxLqkntWNbrg==", "type": "package", "path": "microsoft.visualstudio.editor/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Editor.dll", "lib/net472/Microsoft.VisualStudio.Editor.xml", "microsoft.visualstudio.editor.17.0.491.nupkg.sha512", "microsoft.visualstudio.editor.nuspec"]}, "Microsoft.VisualStudio.GraphModel/17.0.32112.339": {"sha512": "VWjCU0QprN+J0nCKGZEEjz5LG20OsBPcIa4lNijwD3Q1QNpBWMvOrKSOZg05vPAXL/ZTjFog2nwTgkFeaklj/w==", "type": "package", "path": "microsoft.visualstudio.graphmodel/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.GraphModel.dll", "lib/net472/Microsoft.VisualStudio.GraphModel.xml", "microsoft.visualstudio.graphmodel.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.graphmodel.nuspec"]}, "Microsoft.VisualStudio.ImageCatalog/17.0.32112.339": {"sha512": "NTyR0ie18aaCVb/q+rBYhtLWxAdR4afoAdZSc2kCPqKYkusJ7yZj7FxawYp83rYoky3RSnIBRiivBRiNG+Bk2A==", "type": "package", "path": "microsoft.visualstudio.imagecatalog/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.ImageCatalog.dll", "lib/net472/en/Microsoft.VisualStudio.ImageCatalog.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.ImageCatalog.dll", "lib/netcoreapp3.1/en/Microsoft.VisualStudio.ImageCatalog.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.ImageCatalog.dll", "microsoft.visualstudio.imagecatalog.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.imagecatalog.nuspec"]}, "Microsoft.VisualStudio.Imaging/17.0.32112.339": {"sha512": "8yQzAskkLsEuXaI5qO37ZqR7CfHTxkRCyrjIfPKgASj1HZzRjqAfrdTNVw3y88qB3D1+jKNfelgvfLMdk93DEA==", "type": "package", "path": "microsoft.visualstudio.imaging/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Imaging.dll", "lib/net472/Microsoft.VisualStudio.Imaging.xml", "lib/net472/en/Microsoft.VisualStudio.Imaging.resources.dll", "microsoft.visualstudio.imaging.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.imaging.nuspec"]}, "Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime/17.0.32112.339": {"sha512": "lKtjv42SUb1G0JzPF4nQNd5aF1jXiJseSYWdwsCW/bC9xG4/SzSc0P0f/Xpw5OM1yDANGv3G4uIK3XVfohNcnw==", "type": "package", "path": "microsoft.visualstudio.imaging.interop.14.0.designtime/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "lib/net20/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.xml", "lib/net45/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "lib/net45/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.xml", "lib/net472/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "lib/net472/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.xml", "microsoft.visualstudio.imaging.interop.14.0.designtime.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.imaging.interop.14.0.designtime.nuspec"]}, "Microsoft.VisualStudio.Interop/17.0.32112.339": {"sha512": "08DuMTtQv4SGeevgtxFhUWjp3LhreUBCnoezmfI2gWkyOAgUds2K0I1FornQgQdO6fWJ/zjpkDGUBT7yaKFS+Q==", "type": "package", "path": "microsoft.visualstudio.interop/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Interop.dll", "lib/net20/Microsoft.VisualStudio.Interop.xml", "lib/net45/Microsoft.VisualStudio.Interop.dll", "lib/net45/Microsoft.VisualStudio.Interop.xml", "lib/net472/Microsoft.VisualStudio.Interop.dll", "lib/net472/Microsoft.VisualStudio.Interop.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Interop.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Interop.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Interop.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Interop.xml", "microsoft.visualstudio.interop.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.interop.nuspec"]}, "Microsoft.VisualStudio.Language/17.0.491": {"sha512": "27NJeFLrXHWq7nBvatMfSCHkH+nJWtm9ZYwxXixRXrAKehk0i/kJqDvSkTaqnBSmhDE7qBWs7v7xa2FA7M6T5w==", "type": "package", "path": "microsoft.visualstudio.language/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Language.dll", "lib/net472/Microsoft.VisualStudio.Language.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Language.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Language.xml", "microsoft.visualstudio.language.17.0.491.nupkg.sha512", "microsoft.visualstudio.language.nuspec"]}, "Microsoft.VisualStudio.Language.Intellisense/17.0.491": {"sha512": "rTTdQf4dI8EqTr9xkgFJn/aL5UVHHDhQUw0MtDCj9sA0tnCP93/G7aye8RIHjsfuauY27WG7wkO1l2yMoBixPA==", "type": "package", "path": "microsoft.visualstudio.language.intellisense/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Language.Intellisense.dll", "lib/net472/Microsoft.VisualStudio.Language.Intellisense.xml", "microsoft.visualstudio.language.intellisense.17.0.491.nupkg.sha512", "microsoft.visualstudio.language.intellisense.nuspec"]}, "Microsoft.VisualStudio.Language.NavigateTo.Interfaces/17.0.491": {"sha512": "lVxnAnNlSPXb2dwyDwfCiUjMkjMnEfNLg/njux2yuFG3cqfXFoKaay/70i0SdhT8C1Ij5DtKEAqdX+FnoGC3KQ==", "type": "package", "path": "microsoft.visualstudio.language.navigateto.interfaces/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Language.NavigateTo.Interfaces.dll", "lib/net472/Microsoft.VisualStudio.Language.NavigateTo.Interfaces.xml", "microsoft.visualstudio.language.navigateto.interfaces.17.0.491.nupkg.sha512", "microsoft.visualstudio.language.navigateto.interfaces.nuspec"]}, "Microsoft.VisualStudio.Language.StandardClassification/17.0.491": {"sha512": "rUEAIlvi8YlCdlMDig9h/O+icH0mCgRtTjUcx/PQF4+eZ9mF7FyzW1BDrn3WR7HD3SN/r4Xs11lkfUPOmL6C6A==", "type": "package", "path": "microsoft.visualstudio.language.standardclassification/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Language.StandardClassification.dll", "lib/net472/Microsoft.VisualStudio.Language.StandardClassification.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Language.StandardClassification.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Language.StandardClassification.xml", "microsoft.visualstudio.language.standardclassification.17.0.491.nupkg.sha512", "microsoft.visualstudio.language.standardclassification.nuspec"]}, "Microsoft.VisualStudio.LanguageServer.Client/17.0.5165": {"sha512": "6HJOD0AlDqbNOKZM8BXCumyUnzKsm/oi+fovQoC2plV7jBfepsAgjX6UvleC+Xe/v7PnTg3u1H8OpkDMFfdHoQ==", "type": "package", "path": "microsoft.visualstudio.languageserver.client/17.0.5165", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.LanguageServer.Client.dll", "lib/net472/Microsoft.VisualStudio.LanguageServer.Client.xml", "lib/net472/cs/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/de/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/es/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/it/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/pl/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/pt-BR/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.LanguageServer.Client.resources.dll", "license.txt", "microsoft.visualstudio.languageserver.client.17.0.5165.nupkg.sha512", "microsoft.visualstudio.languageserver.client.nuspec"]}, "Microsoft.VisualStudio.OLE.Interop/17.0.32112.339": {"sha512": "RoAHPuBUsoCz2hNy3McBEQusfKLqFm5QSqbEOnXuWkmBVbEzPTMy0VTVE6/AAELY61qUA/2NDxtGXQGgr9lb6Q==", "type": "package", "path": "microsoft.visualstudio.ole.interop/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.OLE.Interop.dll", "lib/net20/Microsoft.VisualStudio.OLE.Interop.xml", "lib/net45/Microsoft.VisualStudio.OLE.Interop.dll", "lib/net45/Microsoft.VisualStudio.OLE.Interop.xml", "lib/net472/Microsoft.VisualStudio.OLE.Interop.dll", "lib/net472/Microsoft.VisualStudio.OLE.Interop.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.OLE.Interop.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.OLE.Interop.xml", "lib/netstandard2.0/Microsoft.VisualStudio.OLE.Interop.dll", "lib/netstandard2.0/Microsoft.VisualStudio.OLE.Interop.xml", "microsoft.visualstudio.ole.interop.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.ole.interop.nuspec"]}, "Microsoft.VisualStudio.Package.LanguageService.15.0/17.0.32112.339": {"sha512": "mk+bfs6xJYmlRNirrow0w2dzZabMoEoFt+lw2PNv9mORYvmeKFcfsohTX4/iHoESwKB4ZK63fpWSw5+nCqt03w==", "type": "package", "path": "microsoft.visualstudio.package.languageservice.15.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.VisualStudio.Package.LanguageService.15.0.dll", "lib/net45/Microsoft.VisualStudio.Package.LanguageService.15.0.xml", "microsoft.visualstudio.package.languageservice.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.package.languageservice.15.0.nuspec"]}, "Microsoft.VisualStudio.ProjectAggregator/17.0.32112.339": {"sha512": "+3Sq3yJKscJM7CzP9c5laUynVNynttqoldjgQ42nMJOoDuyj+l4577maB5CYM+kge9c8nQ61lx1qJioKMMKpKQ==", "type": "package", "path": "microsoft.visualstudio.projectaggregator/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.ProjectAggregator.dll", "lib/net472/Microsoft.VisualStudio.ProjectAggregator.xml", "lib/netstandard2.0/Microsoft.VisualStudio.ProjectAggregator.dll", "lib/netstandard2.0/Microsoft.VisualStudio.ProjectAggregator.xml", "microsoft.visualstudio.projectaggregator.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.projectaggregator.nuspec"]}, "Microsoft.VisualStudio.RemoteControl/16.3.41": {"sha512": "Q9lz2anDPJxDLznQRaybv21aY3qgQJmGJiUonH8z2D0XAgKMlMelsu9bg9zhnKCxtA/jreRAM3Md2W6thiDOwQ==", "type": "package", "path": "microsoft.visualstudio.remotecontrol/16.3.41", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/net45/Microsoft.VisualStudio.RemoteControl.dll", "lib/net45/Microsoft.VisualStudio.RemoteControl.xml", "lib/netstandard2.0/Microsoft.VisualStudio.RemoteControl.dll", "lib/netstandard2.0/Microsoft.VisualStudio.RemoteControl.xml", "microsoft.visualstudio.remotecontrol.16.3.41.nupkg.sha512", "microsoft.visualstudio.remotecontrol.nuspec"]}, "Microsoft.VisualStudio.RpcContracts/17.0.51": {"sha512": "4cMhOmJJl18BU+LTrcjNTLDqWBuCN5t87fB64n7UNyKLs03o4UVNKEjsUwlo6USbccGfoyba4mWPWIo7vL0qkA==", "type": "package", "path": "microsoft.visualstudio.rpccontracts/17.0.51", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "PackageIcon.png", "lib/net5.0/Microsoft.VisualStudio.RpcContracts.dll", "lib/net5.0/Microsoft.VisualStudio.RpcContracts.xml", "lib/netstandard2.0/Microsoft.VisualStudio.RpcContracts.dll", "lib/netstandard2.0/Microsoft.VisualStudio.RpcContracts.xml", "microsoft.visualstudio.rpccontracts.17.0.51.nupkg.sha512", "microsoft.visualstudio.rpccontracts.nuspec"]}, "Microsoft.VisualStudio.SDK/17.0.32112.339": {"sha512": "IPWNUfK7m4xitpW+8Uua00APqglcpFckD6a1hY8ZJJXnjF+dLuI4I356hfFLscdimeaLLcGdYU3OzEDsAZdfSg==", "type": "package", "path": "microsoft.visualstudio.sdk/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "microsoft.visualstudio.sdk.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.sdk.nuspec"]}, "Microsoft.VisualStudio.SDK.Analyzers/16.10.10": {"sha512": "LuhBHy7MJJ5SjpS7J2GuHqPyL1VeqXUwYc+mTagaUCzXbNwJmLcSUAioCyQyAzPIn6qtnzuM5Lz6ULOQS3ifUA==", "type": "package", "path": "microsoft.visualstudio.sdk.analyzers/16.10.10", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "analyzers/cs/Microsoft.VisualStudio.SDK.Analyzers.CodeFixes.dll", "analyzers/cs/Microsoft.VisualStudio.SDK.Analyzers.CodeFixes.pdb", "analyzers/cs/Microsoft.VisualStudio.SDK.Analyzers.dll", "analyzers/cs/Microsoft.VisualStudio.SDK.Analyzers.pdb", "build/AdditionalFiles/BannedSymbols.txt", "build/AdditionalFiles/vs-threading.LegacyThreadSwitchingMembers.txt", "build/AdditionalFiles/vs-threading.MainThreadAssertingMethods.txt", "build/AdditionalFiles/vs-threading.MainThreadSwitchingMethods.txt", "build/AdditionalFiles/vs-threading.MembersRequiringMainThread.txt", "build/Microsoft.VisualStudio.SDK.Analyzers.targets", "microsoft.visualstudio.sdk.analyzers.16.10.10.nupkg.sha512", "microsoft.visualstudio.sdk.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.0.4496": {"sha512": "EzqTgjNmZeWVSkJnZI2QkdWPksMDjzbtj6pKnvDOkPdWu56iNuJabQQ6J9QxtDadiJ219tM9dltyCu0F5iBdNQ==", "type": "package", "path": "microsoft.visualstudio.setup.configuration.interop/3.0.4496", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.VisualStudio.Setup.Configuration.Interop.targets", "lib/net35/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "lib/net35/Microsoft.VisualStudio.Setup.Configuration.Interop.xml", "lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.xml", "microsoft.visualstudio.setup.configuration.interop.3.0.4496.nupkg.sha512", "microsoft.visualstudio.setup.configuration.interop.nuspec"]}, "Microsoft.VisualStudio.Shell.15.0/17.0.32112.339": {"sha512": "rB5qBjrlhEx3dmspJLVfvtgSgMhZBysQ1hxIzMJSorz5A72lCvH12Hk46qhcDu9Q1bIptC1MLJrpJbGHzMjPvQ==", "type": "package", "path": "microsoft.visualstudio.shell.15.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Shell.15.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.15.0.xml", "microsoft.visualstudio.shell.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.15.0.nuspec"]}, "Microsoft.VisualStudio.Shell.Design/17.0.32112.339": {"sha512": "gw9P6IIj68zrhdQwKrFzvTk1cTxhi6wrM5IdJbEl4HeFwFgUSyNBYGD5+nuNSHPyQtzNSl0TaRtca8wW85HO9w==", "type": "package", "path": "microsoft.visualstudio.shell.design/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Shell.Design.dll", "lib/net472/Microsoft.VisualStudio.Shell.Design.xml", "microsoft.visualstudio.shell.design.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.design.nuspec"]}, "Microsoft.VisualStudio.Shell.Framework/17.0.32112.339": {"sha512": "5S/UgMS1SOncKVHioH/GTk2w34nkbmDwcDe08FK2GnOGPl/Adeo0tmM5J6Rd3oXY113nZQjaH7zdNKwwydUzZw==", "type": "package", "path": "microsoft.visualstudio.shell.framework/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Shell.Framework.dll", "lib/net472/Microsoft.VisualStudio.Shell.Framework.xml", "microsoft.visualstudio.shell.framework.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.framework.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop/17.0.32112.339": {"sha512": "uoIhPfs3KOXLthZOE0/eaEUNuM2YDHL/CwkJdGPcW2jz2eArhtkNLKvBtNvDYONbBuck9pD5mTq/eqqgREHe3A==", "type": "package", "path": "microsoft.visualstudio.shell.interop/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.xml", "microsoft.visualstudio.shell.interop.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop.10.0/17.0.32112.339": {"sha512": "Q/3fUudJA/FHGVbJ/PSD0VNLxTegn+ijSc/lcRu848NvWIGhNr21hbgI3+Bxfegm54+/NYr/+NRnYADtFXJ8ZQ==", "type": "package", "path": "microsoft.visualstudio.shell.interop.10.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.10.0.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.10.0.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.10.0.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.10.0.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.10.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.10.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.10.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.10.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.10.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.10.0.xml", "microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.10.0.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop.11.0/17.0.32112.339": {"sha512": "09Zy3mg69PXAZfwW+r6cwWCM7dafANds4VnXrmub7yN/+QVPZLS3lj6w6yGb4fDodlw6hkaqt7c/fifwGu5R0A==", "type": "package", "path": "microsoft.visualstudio.shell.interop.11.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.11.0.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.11.0.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.11.0.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.11.0.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.11.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.11.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.11.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.11.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.11.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.11.0.xml", "microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.11.0.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop.12.0/17.0.32112.339": {"sha512": "HNK8KqizBkUUw34QUfniwIqvXALufxe7m7t7wpichO6YzGI9jHTFsQFYB+6LEup4PohFQTkEuqDvM/RUyEwQow==", "type": "package", "path": "microsoft.visualstudio.shell.interop.12.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.12.0.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.12.0.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.12.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.12.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.12.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.12.0.xml", "microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.12.0.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop.8.0/17.0.32112.339": {"sha512": "tEKOhZx95dqNgaSXoBBzRSPJBmHt6DMXzZCrYf/rNprFfUKlFFEJhRJkGpw0HRAOf5sC32AoQUwPQiJYkLK1Qg==", "type": "package", "path": "microsoft.visualstudio.shell.interop.8.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.8.0.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.8.0.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.8.0.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.8.0.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.8.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.8.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.8.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.8.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.8.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.8.0.xml", "microsoft.visualstudio.shell.interop.8.0.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.8.0.nuspec"]}, "Microsoft.VisualStudio.Shell.Interop.9.0/17.0.32112.339": {"sha512": "nPN7SXNy9ur0eEcWoyawB1V8/mLF7uiDZuQNAx+usiLCJQbPfdjaQoTAxl4f/W6l5iKSFBdWP86EAHgE7J/N+g==", "type": "package", "path": "microsoft.visualstudio.shell.interop.9.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.Shell.Interop.9.0.dll", "lib/net20/Microsoft.VisualStudio.Shell.Interop.9.0.xml", "lib/net45/Microsoft.VisualStudio.Shell.Interop.9.0.dll", "lib/net45/Microsoft.VisualStudio.Shell.Interop.9.0.xml", "lib/net472/Microsoft.VisualStudio.Shell.Interop.9.0.dll", "lib/net472/Microsoft.VisualStudio.Shell.Interop.9.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.9.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Shell.Interop.9.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.9.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Shell.Interop.9.0.xml", "microsoft.visualstudio.shell.interop.********.32112.339.nupkg.sha512", "microsoft.visualstudio.shell.interop.9.0.nuspec"]}, "Microsoft.VisualStudio.TaskRunnerExplorer.14.0/14.0.0": {"sha512": "iZpAv8bEWjkyxFF1GIcSOfldqP/umopJKnJGKHa0vg8KR7ZY3u3dWtJmwO4w3abIx+176SIkQe78y5A+/Md7FA==", "type": "package", "path": "microsoft.visualstudio.taskrunnerexplorer.14.0/14.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Microsoft.VisualStudio.TaskRunnerExplorer.14.0.dll", "microsoft.visualstudio.taskrunnerexplorer.14.0.14.0.0.nupkg.sha512", "microsoft.visualstudio.taskrunnerexplorer.14.0.nuspec"]}, "Microsoft.VisualStudio.Telemetry/16.3.250": {"sha512": "Ijo4HUinCwSdgegeXXzfEYmWuVLC1o9CI3FXW2x4CPKoYemlrN6xcGDUy4oKRxyOsFkjAaiRxR/X9TOgy2xVsQ==", "type": "package", "path": "microsoft.visualstudio.telemetry/16.3.250", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/net45/Microsoft.VisualStudio.Telemetry.dll", "lib/net45/Microsoft.VisualStudio.Telemetry.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Telemetry.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Telemetry.xml", "microsoft.visualstudio.telemetry.16.3.250.nupkg.sha512", "microsoft.visualstudio.telemetry.nuspec"]}, "Microsoft.VisualStudio.Text.Data/17.0.491": {"sha512": "hyZ/WwVC0kWJAdDNXvsKhcgps2LgoiEvyGWZPKyStTBIJD6jIJCkhBeKGTk34hNhtjFGY5CVA7A3PJzSItRpHA==", "type": "package", "path": "microsoft.visualstudio.text.data/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Text.Data.dll", "lib/net472/Microsoft.VisualStudio.Text.Data.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Text.Data.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Text.Data.xml", "microsoft.visualstudio.text.data.17.0.491.nupkg.sha512", "microsoft.visualstudio.text.data.nuspec"]}, "Microsoft.VisualStudio.Text.Logic/17.0.491": {"sha512": "lixP4/8JbampXzaJIY30+/a0pDjwTBunFjGKdJCFGGh0Mf7A4dQup0/1RX9nHNH320Bu1FTiKQQIERMFJVs18g==", "type": "package", "path": "microsoft.visualstudio.text.logic/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Text.Logic.dll", "lib/net472/Microsoft.VisualStudio.Text.Logic.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Text.Logic.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Text.Logic.xml", "microsoft.visualstudio.text.logic.17.0.491.nupkg.sha512", "microsoft.visualstudio.text.logic.nuspec"]}, "Microsoft.VisualStudio.Text.UI/17.0.491": {"sha512": "RtCGQDf28SjJH2XPMsTtCUpRKqNNEbtihE7ifu0yH81MOBUywUrRT4Z6aEK8hFCxA1trZMcAQ2M4pU6wzh7Llg==", "type": "package", "path": "microsoft.visualstudio.text.ui/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Text.UI.dll", "lib/net472/Microsoft.VisualStudio.Text.UI.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Text.UI.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Text.UI.xml", "microsoft.visualstudio.text.ui.17.0.491.nupkg.sha512", "microsoft.visualstudio.text.ui.nuspec"]}, "Microsoft.VisualStudio.Text.UI.Wpf/17.0.491": {"sha512": "X4w2rHkDG52IuEMFWTvPG1CjrGFCfJ6zr/BNT2KeKNmt73GIeE/7ww6a8f7TbJ1hRm9MepulhYyaOZFklw/RXg==", "type": "package", "path": "microsoft.visualstudio.text.ui.wpf/17.0.491", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Text.UI.Wpf.dll", "lib/net472/Microsoft.VisualStudio.Text.UI.Wpf.xml", "microsoft.visualstudio.text.ui.wpf.17.0.491.nupkg.sha512", "microsoft.visualstudio.text.ui.wpf.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop/17.0.32112.339": {"sha512": "gDDPA1CCLuTxHv8uqaCI7ngn1T+NqcJX7LAOwJzl4y1WEOchbCHKSAtMtTzS0nzKNAHz7axXASJkOZ8pG6oYnA==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.xml", "microsoft.visualstudio.textmanager.interop.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop.10.0/17.0.32112.339": {"sha512": "I6bmugRgU8/nQgu3dBqxxkBuMSyBTCGy/+fTyEnXI8Zo1bg7xaW4fxny1mgmz75G8xdLjuy0ool8RqoiLa9ptQ==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop.10.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.10.0.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.10.0.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.10.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.10.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.10.0.xml", "microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.10.0.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop.11.0/17.0.32112.339": {"sha512": "VQsGnqN0xDIwjg0jnBOz8W0TI5M5HaB+phL2Q8E0BtZyKClYj5UE5xMECwWSFBuwnh9VjdgCZfL19SQ3fdMipQ==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop.11.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.11.0.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.11.0.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.11.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.11.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.11.0.xml", "microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.11.0.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop.12.0/17.0.32112.339": {"sha512": "VKrnM9GFOzTZPjD8Zjvto24vfMSbHkAnV+LbSDNaRjYPKCga2SdMeXLJ+3B6NknK2zgmiPunnJ6KBJcb0TJ5Jg==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop.12.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.12.0.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.12.0.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.12.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.12.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.12.0.xml", "microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.12.0.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop.8.0/17.0.32112.339": {"sha512": "7n1lgdlsfW2tbFYWaYZTv7xNckk+laEsjiZ2JC046lujSE9oiYMGFcUG0QJvD0GRJSk66t5UOVL+t6wHdx3TZg==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop.8.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.8.0.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.8.0.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.8.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.8.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.8.0.xml", "microsoft.visualstudio.textmanager.interop.8.0.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.8.0.nuspec"]}, "Microsoft.VisualStudio.TextManager.Interop.9.0/17.0.32112.339": {"sha512": "bXq/bNnFBMLxy1kBtFHrm8ZbjBO8nQZHK3JJsy1Gy3ayLaB/M8uTwR9whwiCK+SkZ5HFzlcmKCNc8yjYnMhBcg==", "type": "package", "path": "microsoft.visualstudio.textmanager.interop.9.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "lib/net20/Microsoft.VisualStudio.TextManager.Interop.9.0.xml", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "lib/net45/Microsoft.VisualStudio.TextManager.Interop.9.0.xml", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "lib/net472/Microsoft.VisualStudio.TextManager.Interop.9.0.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.TextManager.Interop.9.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextManager.Interop.9.0.xml", "microsoft.visualstudio.textmanager.interop.********.32112.339.nupkg.sha512", "microsoft.visualstudio.textmanager.interop.9.0.nuspec"]}, "Microsoft.VisualStudio.TextTemplating/17.0.32112.339": {"sha512": "QtRKGfkVwpy+l2ZYw+fyq5ipUkfIRRAEo9S3pQTq3tDI6yzXXfGngZ8iTnE7qNIpPLM+6L6WlBegEtXPpg8A9Q==", "type": "package", "path": "microsoft.visualstudio.texttemplating/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.TextTemplating.dll", "lib/net472/Microsoft.VisualStudio.TextTemplating.xml", "microsoft.visualstudio.texttemplating.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.texttemplating.nuspec"]}, "Microsoft.VisualStudio.TextTemplating.Interfaces/17.0.32112.339": {"sha512": "YGoq/XBkidUUN0P6e49e6DkdBBqQlghAHUnaB0X96cJORB2Sius9rnEKAj4UZXjs18W64WxQxC0G1jyb12cU8Q==", "type": "package", "path": "microsoft.visualstudio.texttemplating.interfaces/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.dll", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.xml", "microsoft.visualstudio.texttemplating.interfaces.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.texttemplating.interfaces.nuspec"]}, "Microsoft.VisualStudio.TextTemplating.Interfaces.10.0/17.0.32112.339": {"sha512": "tJCT28nLsQKUDiNmJhgzWOISkJu0Vjt35pXgTHGnHQ1/Scg1i+AjwvfHxcN3N188DhS3BRDAL8mNlu9POVremw==", "type": "package", "path": "microsoft.visualstudio.texttemplating.interfaces.10.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.dll", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.xml", "microsoft.visualstudio.texttemplating.interfaces.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.texttemplating.interfaces.10.0.nuspec"]}, "Microsoft.VisualStudio.TextTemplating.Interfaces.11.0/17.0.32112.339": {"sha512": "PjyA01667D12oCy9ZVZfk+kr38KkDxbT4SviQReXrcqHDBglc/H/q+MPyEyL6jVhsqCeAO+s/kdm8+/dPXtIuw==", "type": "package", "path": "microsoft.visualstudio.texttemplating.interfaces.11.0/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.dll", "lib/net472/Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.xml", "lib/netstandard2.0/Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.dll", "lib/netstandard2.0/Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.xml", "microsoft.visualstudio.texttemplating.interfaces.*********.32112.339.nupkg.sha512", "microsoft.visualstudio.texttemplating.interfaces.11.0.nuspec"]}, "Microsoft.VisualStudio.TextTemplating.VSHost/17.0.32112.339": {"sha512": "GhbiGGS1E3LA/uxU23c9H4nBSetP5mfN70r2G0s0Ilkss+qWI2nCicw7iDWynhzuXTtEGxJiKqP+pwDQtLa10g==", "type": "package", "path": "microsoft.visualstudio.texttemplating.vshost/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.TextTemplating.VSHost.dll", "lib/net472/Microsoft.VisualStudio.TextTemplating.VSHost.xml", "microsoft.visualstudio.texttemplating.vshost.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.texttemplating.vshost.nuspec"]}, "Microsoft.VisualStudio.Threading/17.0.64": {"sha512": "HD/yoC7u1Ignj/EsCST4iFXl8zaE+8r2A+4CUkl6GLTJjdNjfl8iNvhqpyK8+DjCMwhyNRRH0I6S6FA37fz95Q==", "type": "package", "path": "microsoft.visualstudio.threading/17.0.64", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Threading.dll", "lib/net472/Microsoft.VisualStudio.Threading.xml", "lib/net472/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Threading.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Threading.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "microsoft.visualstudio.threading.17.0.64.nupkg.sha512", "microsoft.visualstudio.threading.nuspec"]}, "Microsoft.VisualStudio.Threading.Analyzers/17.0.64": {"sha512": "+xz3lAqA3h2/5q6H7Udmz9TsxDQ99O+PjoQ4k4BTO3SfAfyJX7ejh7I1D1N/M/GzGUci9YOUpr6KBO4vXLg+zQ==", "type": "package", "path": "microsoft.visualstudio.threading.analyzers/17.0.64", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CSharp.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/cs/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.VisualBasic.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/vb/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "build/AdditionalFiles/vs-threading.LegacyThreadSwitchingMembers.txt", "build/AdditionalFiles/vs-threading.MainThreadAssertingMethods.txt", "build/AdditionalFiles/vs-threading.MainThreadSwitchingMethods.txt", "build/AdditionalFiles/vs-threading.MembersRequiringMainThread.txt", "build/Microsoft.VisualStudio.Threading.Analyzers.targets", "microsoft.visualstudio.threading.analyzers.17.0.64.nupkg.sha512", "microsoft.visualstudio.threading.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.VisualStudio.Utilities/17.0.32112.339": {"sha512": "Tn0ka0UYL36dWCoOC3pAipdZa1oa86aFZC3zi8ljrMpyy5VeOEiBH09OMDfnG2pKxqfmyPfohL1n1fZKP1a66w==", "type": "package", "path": "microsoft.visualstudio.utilities/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net472/Microsoft.VisualStudio.Utilities.dll", "lib/net472/Microsoft.VisualStudio.Utilities.xml", "lib/net472/cs/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/de/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/en/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/es/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/it/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/pl/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/pt/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.Utilities.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Utilities.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.Utilities.xml", "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/de/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/en/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/es/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/it/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/pt/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Utilities.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Utilities.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/en/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/pt/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Utilities.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Utilities.resources.dll", "microsoft.visualstudio.utilities.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.utilities.nuspec"]}, "Microsoft.VisualStudio.Utilities.Internal/16.3.23": {"sha512": "AxbS8vXJj0IjTv67JbmOqwJERYUDE7BHbXYkXGiyqYblizMjhVdohNIethnJX9lVN2RmotN5GQbwLWDoMKatvw==", "type": "package", "path": "microsoft.visualstudio.utilities.internal/16.3.23", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.VisualStudio.Utilities.Internal.dll", "lib/net45/Microsoft.VisualStudio.Utilities.Internal.xml", "lib/netstandard2.0/Microsoft.VisualStudio.Utilities.Internal.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Utilities.Internal.xml", "microsoft.visualstudio.utilities.internal.16.3.23.nupkg.sha512", "microsoft.visualstudio.utilities.internal.nuspec"]}, "Microsoft.VisualStudio.Validation/17.0.28": {"sha512": "qT+0Qv7lxLt7NKQjkroi34s8cDXVPWA3vDkvoFZwM9PRmZ28aKrMLaQRnkT7rgBYLf+mNtr2najktKUzkAtP6Q==", "type": "package", "path": "microsoft.visualstudio.validation/17.0.28", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "microsoft.visualstudio.validation.17.0.28.nupkg.sha512", "microsoft.visualstudio.validation.nuspec"]}, "Microsoft.VisualStudio.VCProjectEngine/17.0.32112.339": {"sha512": "zU+JQ4hYKmwTgkr43xvPwec0rFvC/NiVStN0Ji+ODYtALLBew0jwwvEc6BUc12Sztd0/Ty/vRKSoGaZHXRKrvw==", "type": "package", "path": "microsoft.visualstudio.vcprojectengine/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.VisualStudio.VCProjectEngine.dll", "lib/netstandard2.0/Microsoft.VisualStudio.VCProjectEngine.dll", "microsoft.visualstudio.vcprojectengine.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.vcprojectengine.nuspec"]}, "Microsoft.VisualStudio.VSHelp/17.0.32112.339": {"sha512": "C/swqwbpdqXbwazfwm+lzIgs5+swfAmBArvn6z1QTw16dl7vcSiNXoeM6B6T3pjmN75pkWqn6DB1NHaqkiRdFQ==", "type": "package", "path": "microsoft.visualstudio.vshelp/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.VSHelp.dll", "lib/net20/Microsoft.VisualStudio.VSHelp.xml", "lib/net45/Microsoft.VisualStudio.VSHelp.dll", "lib/net45/Microsoft.VisualStudio.VSHelp.xml", "lib/net472/Microsoft.VisualStudio.VSHelp.dll", "lib/net472/Microsoft.VisualStudio.VSHelp.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.VSHelp.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.VSHelp.xml", "lib/netstandard2.0/Microsoft.VisualStudio.VSHelp.dll", "lib/netstandard2.0/Microsoft.VisualStudio.VSHelp.xml", "microsoft.visualstudio.vshelp.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.vshelp.nuspec"]}, "Microsoft.VisualStudio.VSHelp80/17.0.32112.339": {"sha512": "86kawz3UgbScN8FDbGD0LPC4cqodEpwHUEz7/0rmLsjMunLGIWeEKxxdPH2P2oyKkGQqHRKJfAZgK6XobRaDsw==", "type": "package", "path": "microsoft.visualstudio.vshelp80/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.VSHelp80.dll", "lib/net20/Microsoft.VisualStudio.VSHelp80.xml", "lib/net45/Microsoft.VisualStudio.VSHelp80.dll", "lib/net45/Microsoft.VisualStudio.VSHelp80.xml", "lib/net472/Microsoft.VisualStudio.VSHelp80.dll", "lib/net472/Microsoft.VisualStudio.VSHelp80.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.VSHelp80.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.VSHelp80.xml", "lib/netstandard2.0/Microsoft.VisualStudio.VSHelp80.dll", "lib/netstandard2.0/Microsoft.VisualStudio.VSHelp80.xml", "microsoft.visualstudio.vshelp80.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.vshelp80.nuspec"]}, "Microsoft.VisualStudio.WCFReference.Interop/17.0.32112.339": {"sha512": "fFWLuvb1wGtZl6VfJE0mIwURFWyOnUlbOM9BWjDydr+t7bKCrXghxi5qZ5NNKHL3Ee13NRiE32oqijpYgX6zsg==", "type": "package", "path": "microsoft.visualstudio.wcfreference.interop/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/Microsoft.VisualStudio.WCFReference.Interop.dll", "lib/net20/Microsoft.VisualStudio.WCFReference.Interop.xml", "lib/net45/Microsoft.VisualStudio.WCFReference.Interop.dll", "lib/net45/Microsoft.VisualStudio.WCFReference.Interop.xml", "lib/net472/Microsoft.VisualStudio.WCFReference.Interop.dll", "lib/net472/Microsoft.VisualStudio.WCFReference.Interop.xml", "lib/netcoreapp3.1/Microsoft.VisualStudio.WCFReference.Interop.dll", "lib/netcoreapp3.1/Microsoft.VisualStudio.WCFReference.Interop.xml", "lib/netstandard2.0/Microsoft.VisualStudio.WCFReference.Interop.dll", "lib/netstandard2.0/Microsoft.VisualStudio.WCFReference.Interop.xml", "microsoft.visualstudio.wcfreference.interop.17.0.32112.339.nupkg.sha512", "microsoft.visualstudio.wcfreference.interop.nuspec"]}, "Microsoft.VisualStudio.Web.BrowserLink.12.0/12.0.0": {"sha512": "HeuaZh8+wNVdwx7VF8guFGH2Z2zH+FYxWBsRNp+FjjlmrhCfM7GUQV5azaTv/bN5TPaK8ALJoP9UX5o1FB5k1A==", "type": "package", "path": "microsoft.visualstudio.web.browserlink.12.0/12.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Microsoft.VisualStudio.Web.BrowserLink.12.0.dll", "lib/net40/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/cs/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/de/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/es/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/fr/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/it/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/ja/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/ko/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/pl/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/pt-br/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/ru/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/tr/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/za-Hant/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "lib/net40/zh-<PERSON>/Microsoft.VisualStudio.Web.BrowserLink.12.0.xml", "microsoft.visualstudio.web.browserlink.12.0.12.0.0.nupkg.sha512", "microsoft.visualstudio.web.browserlink.12.0.nuspec"]}, "Microsoft.VSSDK.BuildTools/17.9.3168": {"sha512": "V6IEXVCt1W/jMnbGg9AYfUDSmaim6FauWzeyRsXk8UFVPYFxYDXc+a4Q8OQY0Lh5LQOWdkzYfeuXIzp37CcLHQ==", "type": "package", "path": "microsoft.vssdk.buildtools/17.9.3168", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE.txt", "PackageIcon.png", "build/Microsoft.VSSDK.BuildTools.props", "build/Microsoft.VSSDK.BuildTools.targets", "license.txt", "microsoft.vssdk.buildtools.17.9.3168.nupkg.sha512", "microsoft.vssdk.buildtools.nuspec", "tools/Microsoft.VisualStudio.ExtensionEngine.dll", "tools/Microsoft.VisualStudio.ExtensionEngine.dll.config", "tools/Microsoft.VisualStudio.ExtensionEngine.pdb", "tools/Microsoft.VisualStudio.ExtensionEngineContract.dll", "tools/Microsoft.VisualStudio.ExtensionEngineContract.dll.config", "tools/Microsoft.VisualStudio.ExtensionEngineContract.pdb", "tools/vssdk/Empty.resx", "tools/vssdk/MessagePack.Annotations.dll", "tools/vssdk/MessagePack.dll", "tools/vssdk/Microsoft.Bcl.AsyncInterfaces.dll", "tools/vssdk/Microsoft.IO.Redist.dll", "tools/vssdk/Microsoft.Internal.VisualStudio.Shell.Framework.dll", "tools/vssdk/Microsoft.VisualStudio.ExtensionEngine.dll", "tools/vssdk/Microsoft.VisualStudio.ExtensionEngineContract.dll", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.17.0.dll", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.17.0.dll.config", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.17.0.pdb", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.dll", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.dll.config", "tools/vssdk/Microsoft.VisualStudio.Sdk.BuildTasks.pdb", "tools/vssdk/Microsoft.VisualStudio.Sdk.Common.targets", "tools/vssdk/Microsoft.VisualStudio.Setup.Common.dll", "tools/vssdk/Microsoft.VisualStudio.Shell.15.0.dll", "tools/vssdk/Microsoft.VisualStudio.Shell.Framework.dll", "tools/vssdk/Microsoft.VisualStudio.Threading.dll", "tools/vssdk/Microsoft.VisualStudio.Validation.dll", "tools/vssdk/Microsoft.VsSDK.Cpp.Overrides.targets", "tools/vssdk/Microsoft.VsSDK.Cpp.targets", "tools/vssdk/Microsoft.VsSDK.targets", "tools/vssdk/Nerdbank.Streams.dll", "tools/vssdk/Newtonsoft.Json.dll", "tools/vssdk/PkgDefMgmt.dll", "tools/vssdk/ProjectItemsSchema.xml", "tools/vssdk/System.IO.Pipelines.dll", "tools/vssdk/System.Reflection.Metadata.dll", "tools/vssdk/System.Threading.Tasks.Extensions.dll", "tools/vssdk/bin/ConvertCTCToVSCT.pl", "tools/vssdk/bin/CreatePkgDef.exe", "tools/vssdk/bin/CreatePkgDef.exe.config", "tools/vssdk/bin/CreatePkgDef.pdb", "tools/vssdk/bin/DebugSamples.dll", "tools/vssdk/bin/DebugSamples.dll.config", "tools/vssdk/bin/DebugSamples.pdb", "tools/vssdk/bin/RegRiched20.exe", "tools/vssdk/bin/RegRiched20.exe.config", "tools/vssdk/bin/RegRiched20.pdb", "tools/vssdk/bin/VSCT.exe", "tools/vssdk/bin/VSCT.exe.config", "tools/vssdk/bin/VSCT.pdb", "tools/vssdk/bin/VSCTCompress.dll", "tools/vssdk/bin/VSCTLibrary.dll", "tools/vssdk/bin/VSCTLibrary.pdb", "tools/vssdk/bin/VsixCommandLine.dll", "tools/vssdk/bin/VsixCommandLine.pdb", "tools/vssdk/bin/VsixPublisher.exe", "tools/vssdk/bin/VsixPublisher.exe.config", "tools/vssdk/bin/VsixUtil.exe", "tools/vssdk/bin/VsixUtil.exe.config", "tools/vssdk/bin/arm64/CreatePkgDef.exe", "tools/vssdk/bin/arm64/CreatePkgDef.exe.config", "tools/vssdk/bin/arm64/lib/Microsoft.VisualStudio.Shell.Framework.dll", "tools/vssdk/bin/lib/Dia2Lib.dll", "tools/vssdk/bin/lib/MessagePack.Annotations.dll", "tools/vssdk/bin/lib/MessagePack.dll", "tools/vssdk/bin/lib/Microsoft.Bcl.AsyncInterfaces.dll", "tools/vssdk/bin/lib/Microsoft.Build.Framework.dll", "tools/vssdk/bin/lib/Microsoft.Diagnostics.FastSerialization.dll", "tools/vssdk/bin/lib/Microsoft.Diagnostics.Tracing.TraceEvent.dll", "tools/vssdk/bin/lib/Microsoft.IO.Redist.dll", "tools/vssdk/bin/lib/Microsoft.IdentityModel.JsonWebTokens.dll", "tools/vssdk/bin/lib/Microsoft.IdentityModel.Logging.dll", "tools/vssdk/bin/lib/Microsoft.IdentityModel.Tokens.dll", "tools/vssdk/bin/lib/Microsoft.Internal.Performance.CodeMarkers.DesignTime.dll", "tools/vssdk/bin/lib/Microsoft.Internal.VisualStudio.Interop.dll", "tools/vssdk/bin/lib/Microsoft.Internal.VisualStudio.Shell.Framework.dll", "tools/vssdk/bin/lib/Microsoft.ServiceHub.Framework.dll", "tools/vssdk/bin/lib/Microsoft.ServiceHub.Resources.dll", "tools/vssdk/bin/lib/Microsoft.TeamFoundation.Common.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.ComponentModelHost.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Composition.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.ExtensionEngine.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.ExtensionEngineContract.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.GraphModel.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.ImageCatalog.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Imaging.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Interop.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.ProjectAggregator.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.RemoteControl.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.RpcContracts.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Services.Common.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Services.Gallery.WebApi.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Services.WebApi.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Setup.Common.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Setup.Download.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Setup.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Shell.15.0.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Shell.Framework.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Telemetry.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Threading.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Utilities.dll", "tools/vssdk/bin/lib/Microsoft.VisualStudio.Validation.dll", "tools/vssdk/bin/lib/Microsoft.Win32.Registry.dll", "tools/vssdk/bin/lib/Nerdbank.Streams.dll", "tools/vssdk/bin/lib/Newtonsoft.Json.dll", "tools/vssdk/bin/lib/Nuget.Packaging.Extraction.dll", "tools/vssdk/bin/lib/OSExtensions.dll", "tools/vssdk/bin/lib/StreamJsonRpc.dll", "tools/vssdk/bin/lib/System.Buffers.dll", "tools/vssdk/bin/lib/System.Collections.Immutable.dll", "tools/vssdk/bin/lib/System.Composition.AttributedModel.dll", "tools/vssdk/bin/lib/System.Composition.Convention.dll", "tools/vssdk/bin/lib/System.Composition.Hosting.dll", "tools/vssdk/bin/lib/System.Composition.Runtime.dll", "tools/vssdk/bin/lib/System.Composition.TypedParts.dll", "tools/vssdk/bin/lib/System.Diagnostics.DiagnosticSource.dll", "tools/vssdk/bin/lib/System.IO.Pipelines.dll", "tools/vssdk/bin/lib/System.IdentityModel.Tokens.Jwt.dll", "tools/vssdk/bin/lib/System.Memory.dll", "tools/vssdk/bin/lib/System.Net.Http.Formatting.dll", "tools/vssdk/bin/lib/System.Numerics.Vectors.dll", "tools/vssdk/bin/lib/System.Reflection.Metadata.dll", "tools/vssdk/bin/lib/System.Runtime.CompilerServices.Unsafe.dll", "tools/vssdk/bin/lib/System.Security.AccessControl.dll", "tools/vssdk/bin/lib/System.Security.Principal.Windows.dll", "tools/vssdk/bin/lib/System.Text.Encodings.Web.dll", "tools/vssdk/bin/lib/System.Text.Json.dll", "tools/vssdk/bin/lib/System.Threading.AccessControl.dll", "tools/vssdk/bin/lib/System.Threading.Tasks.Dataflow.dll", "tools/vssdk/bin/lib/System.Threading.Tasks.Extensions.dll", "tools/vssdk/bin/lib/System.ValueTuple.dll", "tools/vssdk/bin/lib/TraceReloggerLib.dll", "tools/vssdk/bin/lib/VsixCommandLine.dll", "tools/vssdk/bin/lib/VsixPublisher.exe", "tools/vssdk/bin/lib/VsixPublisher.exe.config", "tools/vssdk/bin/lib/VsixUtil.exe", "tools/vssdk/bin/lib/VsixUtil.exe.config", "tools/vssdk/bin/lib/vsixpublisher/System.Runtime.CompilerServices.Unsafe.dll", "tools/vssdk/bin/x86/CreatePkgDef.exe", "tools/vssdk/bin/x86/CreatePkgDef.exe.config", "tools/vssdk/bin/x86/VSCTCompress.dll", "tools/vssdk/bin/x86/lib/Microsoft.VisualStudio.Shell.Framework.dll", "tools/vssdk/inc/AppIDCmdUsed.vsct", "tools/vssdk/inc/EmulatorCmdUsed.vsct", "tools/vssdk/inc/KnownImageIds.vsct", "tools/vssdk/inc/Menus.vsct", "tools/vssdk/inc/MnuHelpIds.h", "tools/vssdk/inc/RazorCmdId.h", "tools/vssdk/inc/RazorCmdUsed.vsct", "tools/vssdk/inc/RazorGuids.h", "tools/vssdk/inc/SharedCmdDef.vsct", "tools/vssdk/inc/SharedCmdPlace.vsct", "tools/vssdk/inc/ShellCmdDef.vsct", "tools/vssdk/inc/ShellCmdPlace.vsct", "tools/vssdk/inc/VsDbgCmd.h", "tools/vssdk/inc/VsDbgCmdPlace.vsct", "tools/vssdk/inc/VsDbgCmdUsed.vsct", "tools/vssdk/inc/editids.h", "tools/vssdk/inc/sccmnid.h", "tools/vssdk/inc/sharedids.h", "tools/vssdk/inc/stdidcmd.h", "tools/vssdk/inc/venusids.h", "tools/vssdk/inc/venusmenu.vsct", "tools/vssdk/inc/virtkeys.h", "tools/vssdk/inc/vsdebugguids.h", "tools/vssdk/inc/vsshlids.h", "tools/vssdk/inc/wbids.h", "tools/vssdk/offreg.dll", "tools/vssdk/schemas/PackageLanguagePackManifestSchema.xsd", "tools/vssdk/schemas/PackageManifestSchema.Assets.xsd", "tools/vssdk/schemas/PackageManifestSchema.Dependencies.xsd", "tools/vssdk/schemas/PackageManifestSchema.DesignNamespace.xsd", "tools/vssdk/schemas/PackageManifestSchema.Installation.xsd", "tools/vssdk/schemas/PackageManifestSchema.Installer.xsd", "tools/vssdk/schemas/PackageManifestSchema.Metadata.xsd", "tools/vssdk/schemas/PackageManifestSchema.Prerequisites.xsd", "tools/vssdk/schemas/PackageManifestSchema.xsd", "tools/vssdk/schemas/VSIXLanguagePackSchema.xsd", "tools/vssdk/schemas/VSIXManifestSchema.xsd"]}, "Microsoft.VsSDK.CompatibilityAnalyzer/17.9.3168": {"sha512": "9+JkFppsNMFyFLRNk/tkIymiLNbV5Uote9HZye1Lgv9p/h4iCKtYJhqAKDdsv3dfk+uarKjixYIYzYmdUEl6/Q==", "type": "package", "path": "microsoft.vssdk.compatibilityanalyzer/17.9.3168", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "build/Microsoft.VsSDK.CompatibilityAnalyzer.props", "build/Microsoft.VsSDK.CompatibilityAnalyzer.targets", "license.txt", "microsoft.vssdk.compatibilityanalyzer.17.9.3168.nupkg.sha512", "microsoft.vssdk.compatibilityanalyzer.nuspec", "tools/net472/Microsoft.VisualStudio.Sdk.CompatibilityAnalyzerTasks.dll", "tools/net472/VsixCompatibilityAnalyzer.dll", "tools/net6.0/Microsoft.VisualStudio.Sdk.CompatibilityAnalyzerTasks.dll", "tools/net6.0/System.IO.Packaging.dll", "tools/net6.0/VsixCompatibilityAnalyzer.dll"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Nerdbank.Streams/2.6.81": {"sha512": "htBHFE359qyyFwrvAGvFxrbBAoldZdl0XjtQdDWTJ8t5sWWs7QVXID5y1ZGJE61UgpV5CqWsj/NT0LOAn5GdZw==", "type": "package", "path": "nerdbank.streams/2.6.81", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp2.1/Nerdbank.Streams.dll", "lib/netcoreapp2.1/Nerdbank.Streams.pdb", "lib/netcoreapp2.1/Nerdbank.Streams.xml", "lib/netcoreapp3.1/Nerdbank.Streams.dll", "lib/netcoreapp3.1/Nerdbank.Streams.pdb", "lib/netcoreapp3.1/Nerdbank.Streams.xml", "lib/netstandard2.0/Nerdbank.Streams.dll", "lib/netstandard2.0/Nerdbank.Streams.pdb", "lib/netstandard2.0/Nerdbank.Streams.xml", "nerdbank.streams.2.6.81.nupkg.sha512", "nerdbank.streams.nuspec"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "stdole/17.0.32112.339": {"sha512": "iFNKbab4S9zdnTNxehj3JRH2RTuJ6QN3cAceZLxvcRuBek1g9IpKORf6Nvruy1mNbXEHlQrZRAzEVBFZwv1pkw==", "type": "package", "path": "stdole/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/stdole.dll", "lib/net20/stdole.xml", "lib/net45/stdole.dll", "lib/net45/stdole.xml", "lib/net472/stdole.dll", "lib/net472/stdole.xml", "lib/netcoreapp3.1/stdole.dll", "lib/netcoreapp3.1/stdole.xml", "lib/netstandard2.0/stdole.dll", "lib/netstandard2.0/stdole.xml", "stdole.17.0.32112.339.nupkg.sha512", "stdole.nuspec"]}, "StreamJsonRpc/2.8.28": {"sha512": "i2hKUXJSLEoWpPqQNyISqLDqmFHMiyasjTC/PrrHNWhQyauFeVoebSct3E4OTUzRC1DYjVJ9AMiVbp/uVYLnjQ==", "type": "package", "path": "streamjsonrpc/2.8.28", "files": [".nupkg.metadata", ".signature.p7s", "PackageIcon.png", "lib/netcoreapp2.1/StreamJsonRpc.dll", "lib/netcoreapp2.1/StreamJsonRpc.xml", "lib/netcoreapp2.1/cs/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/de/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/es/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/fr/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/it/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/ja/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/ko/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/pl/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/pt-BR/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/ru/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/tr/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/zh-Hans/StreamJsonRpc.resources.dll", "lib/netcoreapp2.1/zh-Hant/StreamJsonRpc.resources.dll", "lib/netstandard2.0/StreamJsonRpc.dll", "lib/netstandard2.0/StreamJsonRpc.xml", "lib/netstandard2.0/cs/StreamJsonRpc.resources.dll", "lib/netstandard2.0/de/StreamJsonRpc.resources.dll", "lib/netstandard2.0/es/StreamJsonRpc.resources.dll", "lib/netstandard2.0/fr/StreamJsonRpc.resources.dll", "lib/netstandard2.0/it/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ja/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ko/StreamJsonRpc.resources.dll", "lib/netstandard2.0/pl/StreamJsonRpc.resources.dll", "lib/netstandard2.0/pt-BR/StreamJsonRpc.resources.dll", "lib/netstandard2.0/ru/StreamJsonRpc.resources.dll", "lib/netstandard2.0/tr/StreamJsonRpc.resources.dll", "lib/netstandard2.0/zh-<PERSON>/StreamJsonRpc.resources.dll", "lib/netstandard2.0/zh-Hant/StreamJsonRpc.resources.dll", "streamjsonrpc.2.8.28.nupkg.sha512", "streamjsonrpc.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Composition/4.5.0": {"sha512": "+iB9FoZnfdqMEGq6np28X6YNSUrse16CakmIhV3h6PxEWt7jYxUN3Txs1D8MZhhf4QmyvK0F/EcIN0f4gGN0dA==", "type": "package", "path": "system.componentmodel.composition/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcoreapp2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard2.0/System.ComponentModel.Composition.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.composition.4.5.0.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/5.0.1": {"sha512": "uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Pipelines/5.0.1": {"sha512": "qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "type": "package", "path": "system.io.pipelines/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.1.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Net.Http/4.3.4": {"sha512": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "type": "package", "path": "system.net.http/4.3.4", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/netcore50/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.dll", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll", "system.net.http.4.3.4.nupkg.sha512", "system.net.http.nuspec"]}, "System.Net.WebSockets/4.3.0": {"sha512": "u6fFNY5q4T8KerUAVbya7bR6b7muBuSTAersyrihkcmE5QhEOiH3t5rh4il15SexbVlpXFHGuMwr/m8fDrnkQg==", "type": "package", "path": "system.net.websockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.WebSockets.dll", "lib/netstandard1.3/System.Net.WebSockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.xml", "ref/netstandard1.3/de/System.Net.WebSockets.xml", "ref/netstandard1.3/es/System.Net.WebSockets.xml", "ref/netstandard1.3/fr/System.Net.WebSockets.xml", "ref/netstandard1.3/it/System.Net.WebSockets.xml", "ref/netstandard1.3/ja/System.Net.WebSockets.xml", "ref/netstandard1.3/ko/System.Net.WebSockets.xml", "ref/netstandard1.3/ru/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hans/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hant/System.Net.WebSockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.websockets.4.3.0.nupkg.sha512", "system.net.websockets.nuspec"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Private.Uri/4.3.2": {"sha512": "o1+7RJnu3Ik3PazR7Z7tJhjPdE000Eq2KGLLWhqJJKXj04wrS8lwb1OFtDF9jzXXADhUuZNJZlPc98uwwqmpFA==", "type": "package", "path": "system.private.uri/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "system.private.uri.4.3.2.nupkg.sha512", "system.private.uri.nuspec"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.6.0": {"sha512": "j/V5HVvxvBQ7uubYD0PptQW2KGsi1Pc2kZ9yfwLixv3ADdjL/4M78KyC5e+ymW612DY8ZE4PFoZmWpoNmN2mqg==", "type": "package", "path": "system.reflection.emit.lightweight/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.6.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.X509Certificates/4.3.0": {"sha512": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "type": "package", "path": "system.security.cryptography.x509certificates/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.AccessControl/5.0.0": {"sha512": "WJ9w9m4iHJVq0VoH7hZvYAccbRq95itYRhAAXd6M4kVCzLmT6NqTwmSXKwp3oQilWHhYTXgqaIXxBfg8YaqtmA==", "type": "package", "path": "system.threading.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Threading.AccessControl.dll", "lib/net461/System.Threading.AccessControl.dll", "lib/net461/System.Threading.AccessControl.xml", "lib/netstandard1.3/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.xml", "ref/net46/System.Threading.AccessControl.dll", "ref/net461/System.Threading.AccessControl.dll", "ref/net461/System.Threading.AccessControl.xml", "ref/netstandard1.3/System.Threading.AccessControl.dll", "ref/netstandard1.3/System.Threading.AccessControl.xml", "ref/netstandard1.3/de/System.Threading.AccessControl.xml", "ref/netstandard1.3/es/System.Threading.AccessControl.xml", "ref/netstandard1.3/fr/System.Threading.AccessControl.xml", "ref/netstandard1.3/it/System.Threading.AccessControl.xml", "ref/netstandard1.3/ja/System.Threading.AccessControl.xml", "ref/netstandard1.3/ko/System.Threading.AccessControl.xml", "ref/netstandard1.3/ru/System.Threading.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Threading.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Threading.AccessControl.xml", "ref/netstandard2.0/System.Threading.AccessControl.dll", "ref/netstandard2.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net46/System.Threading.AccessControl.dll", "runtimes/win/lib/net461/System.Threading.AccessControl.dll", "runtimes/win/lib/net461/System.Threading.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Threading.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Threading.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Threading.AccessControl.xml", "system.threading.accesscontrol.5.0.0.nupkg.sha512", "system.threading.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Dataflow/5.0.0": {"sha512": "NBp0zSAMZp4muDje6XmbDfmkqw9+qsDCHp+YMEtnVgHEjQZ3Q7MzFTTp3eHqpExn4BwMrS7JkUVOTcVchig4Sw==", "type": "package", "path": "system.threading.tasks.dataflow/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Threading.Tasks.Dataflow.dll", "lib/net461/System.Threading.Tasks.Dataflow.xml", "lib/netstandard1.0/System.Threading.Tasks.Dataflow.dll", "lib/netstandard1.0/System.Threading.Tasks.Dataflow.xml", "lib/netstandard1.1/System.Threading.Tasks.Dataflow.dll", "lib/netstandard1.1/System.Threading.Tasks.Dataflow.xml", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.dll", "lib/netstandard2.0/System.Threading.Tasks.Dataflow.xml", "lib/portable-net45+win8+wpa81/System.Threading.Tasks.Dataflow.dll", "lib/portable-net45+win8+wpa81/System.Threading.Tasks.Dataflow.xml", "system.threading.tasks.dataflow.5.0.0.nupkg.sha512", "system.threading.tasks.dataflow.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "VSLangProj/17.0.32112.339": {"sha512": "xTWujQFrYExLoRCkLSJP7XEgJgw22FwkLTdmeMJ0GajovH5kKe/9nc85wkT0wvQCJdcM90oTU81jR2nSd1x1nA==", "type": "package", "path": "vslangproj/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj.dll", "lib/net20/VSLangProj.xml", "lib/net45/VSLangProj.dll", "lib/net45/VSLangProj.xml", "lib/net472/VSLangProj.dll", "lib/net472/VSLangProj.xml", "lib/netcoreapp3.1/VSLangProj.dll", "lib/netcoreapp3.1/VSLangProj.xml", "lib/netstandard2.0/VSLangProj.dll", "lib/netstandard2.0/VSLangProj.xml", "vslangproj.17.0.32112.339.nupkg.sha512", "vslangproj.nuspec"]}, "VSLangProj100/17.0.32112.339": {"sha512": "KjIfWc5j9WQOTHepcR/5naldWdPDSrq0YZHjWfQ8UIIRyqDdeY46HfTf3lh5nZ3uiShdA7vIlO9ikAkn1Bj4yQ==", "type": "package", "path": "vslangproj100/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj100.dll", "lib/net20/VSLangProj100.xml", "lib/net45/VSLangProj100.dll", "lib/net45/VSLangProj100.xml", "lib/net472/VSLangProj100.dll", "lib/net472/VSLangProj100.xml", "lib/netcoreapp3.1/VSLangProj100.dll", "lib/netcoreapp3.1/VSLangProj100.xml", "lib/netstandard2.0/VSLangProj100.dll", "lib/netstandard2.0/VSLangProj100.xml", "vslangproj100.17.0.32112.339.nupkg.sha512", "vslangproj100.nuspec"]}, "VSLangProj110/17.0.32112.339": {"sha512": "DunTgaU9bgPyCjOOrNVv6JBdxSMW3o7hiVgNEUctIygStM3cVMCwCybX69BGkNZOEFkNMK+MdKEyTLPEAVuNxQ==", "type": "package", "path": "vslangproj110/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj110.dll", "lib/net20/VSLangProj110.xml", "lib/net45/VSLangProj110.dll", "lib/net45/VSLangProj110.xml", "lib/net472/VSLangProj110.dll", "lib/net472/VSLangProj110.xml", "lib/netcoreapp3.1/VSLangProj110.dll", "lib/netcoreapp3.1/VSLangProj110.xml", "lib/netstandard2.0/VSLangProj110.dll", "lib/netstandard2.0/VSLangProj110.xml", "vslangproj110.17.0.32112.339.nupkg.sha512", "vslangproj110.nuspec"]}, "VSLangProj140/17.0.32112.339": {"sha512": "0psy0SHjCR2cfuZOYckfF6fZAdFKbhh26jV/Wx4mggDbHB/f5WIJfRZGkoYuLH+HGe96KSEj+aEg5wUSkPTPFA==", "type": "package", "path": "vslangproj140/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj140.dll", "lib/net20/VSLangProj140.xml", "lib/net45/VSLangProj140.dll", "lib/net45/VSLangProj140.xml", "lib/net472/VSLangProj140.dll", "lib/net472/VSLangProj140.xml", "lib/netcoreapp3.1/VSLangProj140.dll", "lib/netcoreapp3.1/VSLangProj140.xml", "lib/netstandard2.0/VSLangProj140.dll", "lib/netstandard2.0/VSLangProj140.xml", "vslangproj140.17.0.32112.339.nupkg.sha512", "vslangproj140.nuspec"]}, "VSLangProj150/17.0.32112.339": {"sha512": "736mB+vhdHAW8FcGaLEYEVvfdQB0eVrqJVkm5LukxJaWYom1GYqfN58N/qjaBqnGUoDZp7DfTN5k4lltwQg1sg==", "type": "package", "path": "vslangproj150/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj150.dll", "lib/net20/VSLangProj150.xml", "lib/net45/VSLangProj150.dll", "lib/net45/VSLangProj150.xml", "lib/net472/VSLangProj150.dll", "lib/net472/VSLangProj150.xml", "lib/netcoreapp3.1/VSLangProj150.dll", "lib/netcoreapp3.1/VSLangProj150.xml", "lib/netstandard2.0/VSLangProj150.dll", "lib/netstandard2.0/VSLangProj150.xml", "vslangproj150.17.0.32112.339.nupkg.sha512", "vslangproj150.nuspec"]}, "VSLangProj157/17.0.32112.339": {"sha512": "YhPFOow0tTvRqmk/UhtbyHyxA/kliW5//82EAn+tCN3LWGL8fRmvXDiOkaU6y2AJ5r5/6I9rQdgl2dEHfK7I3w==", "type": "package", "path": "vslangproj157/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj157.dll", "lib/net20/VSLangProj157.xml", "lib/net45/VSLangProj157.dll", "lib/net45/VSLangProj157.xml", "lib/net472/VSLangProj157.dll", "lib/net472/VSLangProj157.xml", "lib/netcoreapp3.1/VSLangProj157.dll", "lib/netcoreapp3.1/VSLangProj157.xml", "lib/netstandard2.0/VSLangProj157.dll", "lib/netstandard2.0/VSLangProj157.xml", "vslangproj157.17.0.32112.339.nupkg.sha512", "vslangproj157.nuspec"]}, "VSLangProj158/17.0.32112.339": {"sha512": "QlfJVnLCTXnYxSVCWpNQymZhLlxgY9SvMln8iOsZ+QH04kbN0hyo5kzLmjDi5rCPHLeg7f/GkVqUUbTywsQ62A==", "type": "package", "path": "vslangproj158/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj158.dll", "lib/net20/VSLangProj158.xml", "lib/net45/VSLangProj158.dll", "lib/net45/VSLangProj158.xml", "lib/net472/VSLangProj158.dll", "lib/net472/VSLangProj158.xml", "lib/netcoreapp3.1/VSLangProj158.dll", "lib/netcoreapp3.1/VSLangProj158.xml", "lib/netstandard2.0/VSLangProj158.dll", "lib/netstandard2.0/VSLangProj158.xml", "vslangproj158.17.0.32112.339.nupkg.sha512", "vslangproj158.nuspec"]}, "VSLangProj165/17.0.32112.339": {"sha512": "7KVj+gss94oIu/dvUExr/quq1OA8ywLxHEpz1f50ls8Sjoo0mmurVVpXvC66M2CBUKX6KAWHIkHhcpwlVhuNUw==", "type": "package", "path": "vslangproj165/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj165.dll", "lib/net20/VSLangProj165.xml", "lib/net45/VSLangProj165.dll", "lib/net45/VSLangProj165.xml", "lib/net472/VSLangProj165.dll", "lib/net472/VSLangProj165.xml", "lib/netcoreapp3.1/VSLangProj165.dll", "lib/netcoreapp3.1/VSLangProj165.xml", "lib/netstandard2.0/VSLangProj165.dll", "lib/netstandard2.0/VSLangProj165.xml", "vslangproj165.17.0.32112.339.nupkg.sha512", "vslangproj165.nuspec"]}, "VSLangProj2/17.0.32112.339": {"sha512": "Ja8DHZOuDW0oIvH65FH6xG+iwB0wS5h1bC7YXvEa4VAMeSsk/Ybz9MOoIySt3qgc9nAl+u8gz+EHaB/SG3h3pA==", "type": "package", "path": "vslangproj2/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj2.dll", "lib/net20/VSLangProj2.xml", "lib/net45/VSLangProj2.dll", "lib/net45/VSLangProj2.xml", "lib/net472/VSLangProj2.dll", "lib/net472/VSLangProj2.xml", "lib/netcoreapp3.1/VSLangProj2.dll", "lib/netcoreapp3.1/VSLangProj2.xml", "lib/netstandard2.0/VSLangProj2.dll", "lib/netstandard2.0/VSLangProj2.xml", "vslangproj2.17.0.32112.339.nupkg.sha512", "vslangproj2.nuspec"]}, "VSLangProj80/17.0.32112.339": {"sha512": "iIUIh6PtBdSh5wUh9BUz4rfm3Jyc5DJX+rSTU3wb/vKMYnAgsEDH4aljYDpkWAY6q2IZiTmSZ5nayYlxyKVzLg==", "type": "package", "path": "vslangproj80/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj80.dll", "lib/net20/VSLangProj80.xml", "lib/net45/VSLangProj80.dll", "lib/net45/VSLangProj80.xml", "lib/net472/VSLangProj80.dll", "lib/net472/VSLangProj80.xml", "lib/netcoreapp3.1/VSLangProj80.dll", "lib/netcoreapp3.1/VSLangProj80.xml", "lib/netstandard2.0/VSLangProj80.dll", "lib/netstandard2.0/VSLangProj80.xml", "vslangproj80.17.0.32112.339.nupkg.sha512", "vslangproj80.nuspec"]}, "VSLangProj90/17.0.32112.339": {"sha512": "UWyWaQh+LRIKSOmUy44wP86XuJhdyjLm7/LkGPf9wLuIjXOWrQ+DAO4/RuWzYdevEP/DX9DI87KUwV0Pj1i2Ug==", "type": "package", "path": "vslangproj90/17.0.32112.339", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "PackageIcon.png", "lib/net20/VSLangProj90.dll", "lib/net20/VSLangProj90.xml", "lib/net45/VSLangProj90.dll", "lib/net45/VSLangProj90.xml", "lib/net472/VSLangProj90.dll", "lib/net472/VSLangProj90.xml", "lib/netcoreapp3.1/VSLangProj90.dll", "lib/netcoreapp3.1/VSLangProj90.xml", "lib/netstandard2.0/VSLangProj90.dll", "lib/netstandard2.0/VSLangProj90.xml", "vslangproj90.17.0.32112.339.nupkg.sha512", "vslangproj90.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Microsoft.VSSDK.BuildTools >= 17.8.2369", "Microsoft.VisualStudio.SDK >= 17.0.32112.339"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "projectName": "AugmentVS2022", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"Microsoft.VSSDK.BuildTools": {"target": "Package", "version": "[17.8.2369, )"}, "Microsoft.VisualStudio.SDK": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "target": "Package", "version": "[17.0.32112.339, )"}}}}}, "logs": [{"code": "NU1603", "level": "Warning", "warningLevel": 1, "message": "AugmentVS2022 depends on Microsoft.VSSDK.BuildTools (>= 17.8.2369) but Microsoft.VSSDK.BuildTools 17.8.2369 was not found. Microsoft.VSSDK.BuildTools 17.9.3168 was resolved instead.", "libraryId": "Microsoft.VSSDK.BuildTools", "targetGraphs": [".NETFramework,Version=v4.8"]}]}