{"version": 2, "dgSpecHash": "Nd56HMHhbhk=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\envdte\\17.0.32112.339\\envdte.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\envdte100\\17.0.32112.339\\envdte100.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\envdte80\\17.0.32112.339\\envdte80.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\envdte90\\17.0.32112.339\\envdte90.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\envdte90a\\17.0.32112.339\\envdte90a.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.2.85\\messagepack.2.2.85.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.2.85\\messagepack.annotations.2.2.85.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\5.0.0\\microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\16.5.0\\microsoft.build.framework.16.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.bannedapianalyzers\\3.3.2\\microsoft.codeanalysis.bannedapianalyzers.3.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.analyzers\\3.0.3078\\microsoft.servicehub.analyzers.3.0.3078.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.client\\3.0.3078\\microsoft.servicehub.client.3.0.3078.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.framework\\3.0.3078\\microsoft.servicehub.framework.3.0.3078.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.resources\\3.0.3078\\microsoft.servicehub.resources.3.0.3078.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.commandbars\\17.0.32112.339\\microsoft.visualstudio.commandbars.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.componentmodelhost\\17.0.491\\microsoft.visualstudio.componentmodelhost.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.coreutility\\17.0.491\\microsoft.visualstudio.coreutility.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.10.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.11.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.12.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.14.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.15.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.15.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.16.0\\17.0.32112.339\\microsoft.visualstudio.debugger.interop.16.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interopa\\17.0.32112.339\\microsoft.visualstudio.debugger.interopa.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.designer.interfaces\\17.0.32112.339\\microsoft.visualstudio.designer.interfaces.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.editor\\17.0.491\\microsoft.visualstudio.editor.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.graphmodel\\17.0.32112.339\\microsoft.visualstudio.graphmodel.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imagecatalog\\17.0.32112.339\\microsoft.visualstudio.imagecatalog.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imaging\\17.0.32112.339\\microsoft.visualstudio.imaging.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imaging.interop.14.0.designtime\\17.0.32112.339\\microsoft.visualstudio.imaging.interop.14.0.designtime.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.interop\\17.0.32112.339\\microsoft.visualstudio.interop.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language\\17.0.491\\microsoft.visualstudio.language.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.intellisense\\17.0.491\\microsoft.visualstudio.language.intellisense.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.navigateto.interfaces\\17.0.491\\microsoft.visualstudio.language.navigateto.interfaces.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.standardclassification\\17.0.491\\microsoft.visualstudio.language.standardclassification.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.languageserver.client\\17.0.5165\\microsoft.visualstudio.languageserver.client.17.0.5165.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.ole.interop\\17.0.32112.339\\microsoft.visualstudio.ole.interop.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.package.languageservice.15.0\\17.0.32112.339\\microsoft.visualstudio.package.languageservice.15.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.projectaggregator\\17.0.32112.339\\microsoft.visualstudio.projectaggregator.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.remotecontrol\\16.3.41\\microsoft.visualstudio.remotecontrol.16.3.41.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.rpccontracts\\17.0.51\\microsoft.visualstudio.rpccontracts.17.0.51.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.sdk\\17.0.32112.339\\microsoft.visualstudio.sdk.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.sdk.analyzers\\16.10.10\\microsoft.visualstudio.sdk.analyzers.16.10.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.setup.configuration.interop\\3.0.4496\\microsoft.visualstudio.setup.configuration.interop.3.0.4496.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.15.0\\17.0.32112.339\\microsoft.visualstudio.shell.15.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.design\\17.0.32112.339\\microsoft.visualstudio.shell.design.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.framework\\17.0.32112.339\\microsoft.visualstudio.shell.framework.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop\\17.0.32112.339\\microsoft.visualstudio.shell.interop.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.10.0\\17.0.32112.339\\microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.11.0\\17.0.32112.339\\microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.12.0\\17.0.32112.339\\microsoft.visualstudio.shell.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.8.0\\17.0.32112.339\\microsoft.visualstudio.shell.interop.8.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.9.0\\17.0.32112.339\\microsoft.visualstudio.shell.interop.9.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.taskrunnerexplorer.14.0\\14.0.0\\microsoft.visualstudio.taskrunnerexplorer.14.0.14.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.telemetry\\16.3.250\\microsoft.visualstudio.telemetry.16.3.250.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.data\\17.0.491\\microsoft.visualstudio.text.data.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.logic\\17.0.491\\microsoft.visualstudio.text.logic.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.ui\\17.0.491\\microsoft.visualstudio.text.ui.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.ui.wpf\\17.0.491\\microsoft.visualstudio.text.ui.wpf.17.0.491.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.10.0\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.11.0\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.12.0\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.8.0\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.8.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.9.0\\17.0.32112.339\\microsoft.visualstudio.textmanager.interop.9.0.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating\\17.0.32112.339\\microsoft.visualstudio.texttemplating.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces\\17.0.32112.339\\microsoft.visualstudio.texttemplating.interfaces.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces.10.0\\17.0.32112.339\\microsoft.visualstudio.texttemplating.interfaces.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces.11.0\\17.0.32112.339\\microsoft.visualstudio.texttemplating.interfaces.*********.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.vshost\\17.0.32112.339\\microsoft.visualstudio.texttemplating.vshost.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading\\17.0.64\\microsoft.visualstudio.threading.17.0.64.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading.analyzers\\17.0.64\\microsoft.visualstudio.threading.analyzers.17.0.64.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.utilities\\17.0.32112.339\\microsoft.visualstudio.utilities.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.utilities.internal\\16.3.23\\microsoft.visualstudio.utilities.internal.16.3.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.0.28\\microsoft.visualstudio.validation.17.0.28.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vcprojectengine\\17.0.32112.339\\microsoft.visualstudio.vcprojectengine.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vshelp\\17.0.32112.339\\microsoft.visualstudio.vshelp.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vshelp80\\17.0.32112.339\\microsoft.visualstudio.vshelp80.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.wcfreference.interop\\17.0.32112.339\\microsoft.visualstudio.wcfreference.interop.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.web.browserlink.12.0\\12.0.0\\microsoft.visualstudio.web.browserlink.12.0.12.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.vssdk.buildtools\\17.9.3168\\microsoft.vssdk.buildtools.17.9.3168.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.vssdk.compatibilityanalyzer\\17.9.3168\\microsoft.vssdk.compatibilityanalyzer.17.9.3168.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nerdbank.streams\\2.6.81\\nerdbank.streams.2.6.81.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.any.system.io\\4.3.0\\runtime.any.system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.any.system.runtime\\4.3.0\\runtime.any.system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win.microsoft.win32.primitives\\4.3.0\\runtime.win.microsoft.win32.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stdole\\17.0.32112.339\\stdole.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\streamjsonrpc\\2.8.28\\streamjsonrpc.2.8.28.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\system.collections.immutable.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\4.5.0\\system.componentmodel.composition.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\5.0.1\\system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\5.0.1\\system.io.pipelines.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.websockets\\4.3.0\\system.net.websockets.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.private.uri\\4.3.2\\system.private.uri.4.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.6.0\\system.reflection.emit.lightweight.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\5.0.0\\system.threading.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.dataflow\\5.0.0\\system.threading.tasks.dataflow.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj\\17.0.32112.339\\vslangproj.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj100\\17.0.32112.339\\vslangproj100.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj110\\17.0.32112.339\\vslangproj110.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj140\\17.0.32112.339\\vslangproj140.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj150\\17.0.32112.339\\vslangproj150.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj157\\17.0.32112.339\\vslangproj157.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj158\\17.0.32112.339\\vslangproj158.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj165\\17.0.32112.339\\vslangproj165.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj2\\17.0.32112.339\\vslangproj2.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj80\\17.0.32112.339\\vslangproj80.17.0.32112.339.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj90\\17.0.32112.339\\vslangproj90.17.0.32112.339.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "AugmentVS2022 depends on Microsoft.VSSDK.BuildTools (>= 17.8.2369) but Microsoft.VSSDK.BuildTools 17.8.2369 was not found. Microsoft.VSSDK.BuildTools 17.9.3168 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "libraryId": "Microsoft.VSSDK.BuildTools", "targetGraphs": [".NETFramework,Version=v4.8", ".NETFramework,Version=v4.8/win", ".NETFramework,Version=v4.8/win-arm64", ".NETFramework,Version=v4.8/win-x64", ".NETFramework,Version=v4.8/win-x86"]}]}