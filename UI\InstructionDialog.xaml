<Window x:Class="AugmentVS2022.UI.InstructionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vsshell="clr-namespace:Microsoft.VisualStudio.Shell;assembly=Microsoft.VisualStudio.Shell.15.0"
        mc:Ignorable="d"
        Title="Augment Instructions"
        Height="300" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}">
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Enter your instruction to modify the selected code:"
                   FontWeight="Bold"
                   Margin="0,0,0,10"
                   Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"/>
        
        <!-- Instruction Input -->
        <Border Grid.Row="1" 
                BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                BorderThickness="1"
                Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}">
            <TextBox Name="InstructionTextBox"
                     Text="{Binding Instruction, UpdateSourceTrigger=PropertyChanged}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     VerticalScrollBarVisibility="Auto"
                     Padding="8"
                     BorderThickness="0"
                     Background="Transparent"
                     Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                     FontFamily="Consolas, Courier New, monospace"
                     FontSize="12"/>
        </Border>
        
        <!-- Examples -->
        <StackPanel Grid.Row="2" Margin="0,10,0,10">
            <TextBlock Text="Examples:"
                       FontWeight="Bold"
                       FontSize="11"
                       Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                       Margin="0,0,0,5"/>
            <TextBlock Text="• Add error handling to this method"
                       FontSize="10"
                       Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                       Opacity="0.8"/>
            <TextBlock Text="• Convert this to async/await pattern"
                       FontSize="10"
                       Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                       Opacity="0.8"/>
            <TextBlock Text="• Add XML documentation comments"
                       FontSize="10"
                       Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                       Opacity="0.8"/>
            <TextBlock Text="• Refactor this code to use LINQ"
                       FontSize="10"
                       Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                       Opacity="0.8"/>
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Name="OkButton" 
                    Content="Apply Instruction"
                    Width="120"
                    Height="30"
                    Margin="0,0,10,0"
                    Click="OkButton_Click"
                    IsDefault="True"
                    IsEnabled="{Binding HasInstruction}"/>
            <Button Name="CancelButton" 
                    Content="Cancel"
                    Width="80"
                    Height="30"
                    Click="CancelButton_Click"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
