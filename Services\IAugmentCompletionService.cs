using System.Collections.Generic;
using System.Threading.Tasks;

namespace AugmentVS2022.Services
{
    /// <summary>
    /// Interface for Augment completion service
    /// </summary>
    public interface IAugmentCompletionService
    {
        /// <summary>
        /// Gets whether completions are enabled
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// Gets code completions for the given context
        /// </summary>
        /// <param name="filePath">Path to the file</param>
        /// <param name="code">Current code content</param>
        /// <param name="position">Cursor position</param>
        /// <param name="language">Programming language</param>
        /// <returns>List of completion suggestions</returns>
        Task<List<AugmentCompletion>> GetCompletionsAsync(string filePath, string code, int position, string language);

        /// <summary>
        /// Gets inline completions (like GitHub Copilot)
        /// </summary>
        /// <param name="filePath">Path to the file</param>
        /// <param name="code">Current code content</param>
        /// <param name="position">Cursor position</param>
        /// <param name="language">Programming language</param>
        /// <returns>Inline completion suggestion</returns>
        Task<AugmentInlineCompletion> GetInlineCompletionAsync(string filePath, string code, int position, string language);

        /// <summary>
        /// Accepts an inline completion
        /// </summary>
        /// <param name="completionId">ID of the completion to accept</param>
        Task AcceptInlineCompletionAsync(string completionId);

        /// <summary>
        /// Rejects an inline completion
        /// </summary>
        /// <param name="completionId">ID of the completion to reject</param>
        Task RejectInlineCompletionAsync(string completionId);
    }

    /// <summary>
    /// Represents an inline completion suggestion
    /// </summary>
    public class AugmentInlineCompletion
    {
        public string Id { get; set; }
        public string Text { get; set; }
        public string DisplayText { get; set; }
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public double Confidence { get; set; }
        public string Language { get; set; }
        public bool IsMultiLine { get; set; }
    }
}
