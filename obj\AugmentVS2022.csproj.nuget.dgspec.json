{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "projectName": "AugmentVS2022", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\AugmentVS2022.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"dependencies": {"Microsoft.VSSDK.BuildTools": {"target": "Package", "version": "[17.8.2369, )"}, "Microsoft.VisualStudio.SDK": {"include": "Compile, Build, Native, ContentFiles, Analyzers, BuildTransitive", "target": "Package", "version": "[17.0.32112.339, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}