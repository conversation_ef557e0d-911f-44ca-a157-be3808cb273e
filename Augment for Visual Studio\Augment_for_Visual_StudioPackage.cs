﻿using Microsoft.VisualStudio.Shell;
using System;
using System.ComponentModel.Design;
using System.Runtime.InteropServices;
using System.Threading;
using Augment_for_Visual_Studio.Commands;
using Augment_for_Visual_Studio.UI;
using Task = System.Threading.Tasks.Task;

namespace Augment_for_Visual_Studio
{
    /// <summary>
    /// This is the class that implements the package exposed by this assembly.
    /// </summary>
    /// <remarks>
    /// <para>
    /// The minimum requirement for a class to be considered a valid package for Visual Studio
    /// is to implement the IVsPackage interface and register itself with the shell.
    /// This package uses the helper classes defined inside the Managed Package Framework (MPF)
    /// to do it: it derives from the Package class that provides the implementation of the
    /// IVsPackage interface and uses the registration attributes defined in the framework to
    /// register itself and its components with the shell. These attributes tell the pkgdef creation
    /// utility what data to put into .pkgdef file.
    /// </para>
    /// <para>
    /// To get loaded into VS, the package must be referred by &lt;Asset Type="Microsoft.VisualStudio.VsPackage" ...&gt; in .vsixmanifest file.
    /// </para>
    /// </remarks>
    [PackageRegistration(UseManagedResourcesOnly = true, AllowsBackgroundLoading = true)]
    [Guid(Augment_for_Visual_StudioPackage.PackageGuidString)]
    [ProvideToolWindow(typeof(AugmentChatToolWindow))]
    [ProvideMenuResource("Menus.ctmenu", 1)]
    public sealed class Augment_for_Visual_StudioPackage : AsyncPackage
    {
        /// <summary>
        /// Augment_for_Visual_StudioPackage GUID string.
        /// </summary>
        public const string PackageGuidString = "55c2eb12-478d-4cb4-9e6c-03368f812175";

        #region Package Members

        /// <summary>
        /// Initialization of the package; this method is called right after the package is sited, so this is the place
        /// where you can put all the initialization code that rely on services provided by VisualStudio.
        /// </summary>
        /// <param name="cancellationToken">A cancellation token to monitor for initialization cancellation, which can occur when VS is shutting down.</param>
        /// <param name="progress">A provider for progress updates.</param>
        /// <returns>A task representing the async work of package initialization, or an already completed task if there is none. Do not return null from this method.</returns>
        protected override async Task InitializeAsync(CancellationToken cancellationToken, IProgress<ServiceProgressData> progress)
        {
            // When initialized asynchronously, the current thread may be a background thread at this point.
            // Do any initialization that requires the UI thread after switching to the UI thread.
            await this.JoinableTaskFactory.SwitchToMainThreadAsync(cancellationToken);

            // Initialize commands
            await ShowAugmentChatCommand.InitializeAsync(this);

            // Initialize services
            await InitializeServicesAsync();
        }

        private async Task InitializeServicesAsync()
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();

            // Initialize Augment services
            var authService = new Services.AugmentAuthService();
            var apiService = new Services.AugmentApiService(authService);

            // Register services with the service container
            var serviceContainer = this as IServiceContainer;
            serviceContainer?.AddService(typeof(Services.IAugmentAuthService), authService, true);
            serviceContainer?.AddService(typeof(Services.IAugmentApiService), apiService, true);
        }

        #endregion
    }
}
