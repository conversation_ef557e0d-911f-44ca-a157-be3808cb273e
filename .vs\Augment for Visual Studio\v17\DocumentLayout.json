{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6BBA417B-38DD-51FC-2A80-0317A56CF011}|AugmentVS2022.csproj|c:\\users\\<USER>\\documents\\augment-projects\\augment for visual studio\\instructions\\inlineinstructionprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA417B-38DD-51FC-2A80-0317A56CF011}|AugmentVS2022.csproj|solutionrelative:instructions\\inlineinstructionprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6BBA417B-38DD-51FC-2A80-0317A56CF011}|AugmentVS2022.csproj|C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\completions\\augmentcompletionprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6BBA417B-38DD-51FC-2A80-0317A56CF011}|AugmentVS2022.csproj|solutionrelative:completions\\augmentcompletionprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:1:0:{d212f56b-c48a-434c-a121-1c5d80b59b9f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e506b91c-c606-466a-90a9-123d1d1e12b3}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "InlineInstructionProvider.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Instructions\\InlineInstructionProvider.cs", "RelativeDocumentMoniker": "Instructions\\InlineInstructionProvider.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Instructions\\InlineInstructionProvider.cs", "RelativeToolTip": "Instructions\\InlineInstructionProvider.cs", "ViewState": "AgIAACIAAAAAAAAAAAAtwDAAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T22:54:30.009Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AugmentCompletionProvider.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Completions\\AugmentCompletionProvider.cs", "RelativeDocumentMoniker": "Completions\\AugmentCompletionProvider.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\Completions\\AugmentCompletionProvider.cs", "RelativeToolTip": "Completions\\AugmentCompletionProvider.cs", "ViewState": "AgIAACcAAAAAAAAAAAAewDMAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T22:45:53.938Z"}]}]}]}