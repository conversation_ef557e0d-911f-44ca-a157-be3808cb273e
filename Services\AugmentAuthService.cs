using System;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.VisualStudio.Settings;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Shell.Settings;
using Newtonsoft.Json;

namespace AugmentVS2022.Services
{
    /// <summary>
    /// Implementation of the Augment authentication service
    /// </summary>
    public class AugmentAuthService : IAugmentAuthService
    {
        private const string SETTINGS_COLLECTION = "AugmentVS2022";
        private const string ACCESS_TOKEN_KEY = "AccessToken";
        private const string REFRESH_TOKEN_KEY = "RefreshToken";
        private const string USER_INFO_KEY = "UserInfo";
        
        private const string AUTH_URL = "https://api.augmentcode.com/auth";
        private const string TOKEN_URL = "https://api.augmentcode.com/token";
        private const string USER_URL = "https://api.augmentcode.com/user";

        private readonly HttpClient _httpClient;
        private readonly WritableSettingsStore _settingsStore;
        
        private string _accessToken;
        private string _refreshToken;
        private AugmentUser _currentUser;

        public bool IsAuthenticated => !string.IsNullOrEmpty(_accessToken) && _currentUser != null;
        public string AccessToken => _accessToken;
        public AugmentUser CurrentUser => _currentUser;

        public event EventHandler<AuthenticationChangedEventArgs> AuthenticationChanged;

        public AugmentAuthService()
        {
            _httpClient = new HttpClient();
            
            // Initialize settings store
            ThreadHelper.ThrowIfNotOnUIThread();
            var settingsManager = new ShellSettingsManager(ServiceProvider.GlobalProvider);
            _settingsStore = settingsManager.GetWritableSettingsStore(SettingsScope.UserSettings);
            
            if (!_settingsStore.CollectionExists(SETTINGS_COLLECTION))
            {
                _settingsStore.CreateCollection(SETTINGS_COLLECTION);
            }

            // Load saved tokens
            LoadSavedCredentials();
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // Generate state for OAuth flow
                var state = Guid.NewGuid().ToString();
                var authUrl = $"{AUTH_URL}?response_type=code&client_id=vs2022&state={state}&redirect_uri=http://localhost:8080/callback";

                // Open browser for authentication
                Process.Start(new ProcessStartInfo
                {
                    FileName = authUrl,
                    UseShellExecute = true
                });

                // Start local server to capture callback
                var authCode = await StartCallbackServerAsync();
                
                if (string.IsNullOrEmpty(authCode))
                {
                    return false;
                }

                // Exchange code for tokens
                var tokenResponse = await ExchangeCodeForTokensAsync(authCode);
                
                if (tokenResponse != null)
                {
                    _accessToken = tokenResponse.AccessToken;
                    _refreshToken = tokenResponse.RefreshToken;
                    
                    // Get user info
                    _currentUser = await GetUserInfoAsync();
                    
                    // Save credentials
                    SaveCredentials();
                    
                    // Fire event
                    AuthenticationChanged?.Invoke(this, new AuthenticationChangedEventArgs
                    {
                        IsAuthenticated = true,
                        User = _currentUser,
                        Reason = "Login successful"
                    });
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Login failed: {ex.Message}");
            }

            return false;
        }

        public async Task LogoutAsync()
        {
            _accessToken = null;
            _refreshToken = null;
            _currentUser = null;
            
            // Clear saved credentials
            ClearSavedCredentials();
            
            // Fire event
            AuthenticationChanged?.Invoke(this, new AuthenticationChangedEventArgs
            {
                IsAuthenticated = false,
                User = null,
                Reason = "User logged out"
            });
            
            await Task.CompletedTask;
        }

        public async Task<bool> RefreshTokenAsync()
        {
            if (string.IsNullOrEmpty(_refreshToken))
            {
                return false;
            }

            try
            {
                var refreshResponse = await RefreshAccessTokenAsync(_refreshToken);
                
                if (refreshResponse != null)
                {
                    _accessToken = refreshResponse.AccessToken;
                    if (!string.IsNullOrEmpty(refreshResponse.RefreshToken))
                    {
                        _refreshToken = refreshResponse.RefreshToken;
                    }
                    
                    SaveCredentials();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Token refresh failed: {ex.Message}");
            }

            return false;
        }

        public async Task<bool> ValidateTokenAsync()
        {
            if (string.IsNullOrEmpty(_accessToken))
            {
                return false;
            }

            try
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _accessToken);
                
                var response = await _httpClient.GetAsync(USER_URL);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY))
                {
                    _accessToken = _settingsStore.GetString(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY))
                {
                    _refreshToken = _settingsStore.GetString(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, USER_INFO_KEY))
                {
                    var userJson = _settingsStore.GetString(SETTINGS_COLLECTION, USER_INFO_KEY);
                    _currentUser = JsonConvert.DeserializeObject<AugmentUser>(userJson);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to load saved credentials: {ex.Message}");
            }
        }

        private void SaveCredentials()
        {
            try
            {
                if (!string.IsNullOrEmpty(_accessToken))
                {
                    _settingsStore.SetString(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY, _accessToken);
                }
                
                if (!string.IsNullOrEmpty(_refreshToken))
                {
                    _settingsStore.SetString(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY, _refreshToken);
                }
                
                if (_currentUser != null)
                {
                    var userJson = JsonConvert.SerializeObject(_currentUser);
                    _settingsStore.SetString(SETTINGS_COLLECTION, USER_INFO_KEY, userJson);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to save credentials: {ex.Message}");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, USER_INFO_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, USER_INFO_KEY);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to clear saved credentials: {ex.Message}");
            }
        }

        // Placeholder methods - these would need to be implemented based on Augment's actual API
        private async Task<string> StartCallbackServerAsync()
        {
            // TODO: Implement local HTTP server to capture OAuth callback
            await Task.Delay(1000); // Placeholder
            return "dummy_auth_code";
        }

        private async Task<TokenResponse> ExchangeCodeForTokensAsync(string authCode)
        {
            // TODO: Implement actual token exchange with Augment API
            await Task.Delay(1000); // Placeholder
            return new TokenResponse { AccessToken = "dummy_token", RefreshToken = "dummy_refresh" };
        }

        private async Task<TokenResponse> RefreshAccessTokenAsync(string refreshToken)
        {
            // TODO: Implement actual token refresh with Augment API
            await Task.Delay(1000); // Placeholder
            return new TokenResponse { AccessToken = "new_dummy_token" };
        }

        private async Task<AugmentUser> GetUserInfoAsync()
        {
            // TODO: Implement actual user info retrieval from Augment API
            await Task.Delay(1000); // Placeholder
            return new AugmentUser 
            { 
                Id = "user123", 
                Email = "<EMAIL>", 
                Name = "Test User",
                Plan = "Professional"
            };
        }

        private class TokenResponse
        {
            public string AccessToken { get; set; }
            public string RefreshToken { get; set; }
        }
    }
}
