using System;
using System.Threading.Tasks;

namespace Augment_for_Visual_Studio.Services
{
    /// <summary>
    /// Interface for Augment authentication service
    /// </summary>
    public interface IAugmentAuthService
    {
        /// <summary>
        /// Gets a value indicating whether the user is currently authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Gets the current access token
        /// </summary>
        string AccessToken { get; }

        /// <summary>
        /// Gets the current user information
        /// </summary>
        AugmentUser CurrentUser { get; }

        /// <summary>
        /// Event fired when authentication status changes
        /// </summary>
        event EventHandler<AuthenticationChangedEventArgs> AuthenticationChanged;

        /// <summary>
        /// Initiates the login process
        /// </summary>
        /// <returns>True if login was successful, false otherwise</returns>
        Task<bool> LoginAsync();

        /// <summary>
        /// Logs out the current user
        /// </summary>
        /// <returns>Task representing the logout operation</returns>
        Task LogoutAsync();

        /// <summary>
        /// Refreshes the current access token
        /// </summary>
        /// <returns>True if refresh was successful, false otherwise</returns>
        Task<bool> RefreshTokenAsync();

        /// <summary>
        /// Validates the current token
        /// </summary>
        /// <returns>True if token is valid, false otherwise</returns>
        Task<bool> ValidateTokenAsync();
    }

    /// <summary>
    /// Represents a user in the Augment system
    /// </summary>
    public class AugmentUser
    {
        public string Id { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string Plan { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// Event arguments for authentication status changes
    /// </summary>
    public class AuthenticationChangedEventArgs : EventArgs
    {
        public bool IsAuthenticated { get; set; }
        public AugmentUser User { get; set; }
        public string Reason { get; set; }
    }
}
