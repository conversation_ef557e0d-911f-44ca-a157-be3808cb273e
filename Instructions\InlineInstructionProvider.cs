using System;
using System.Composition;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AugmentVS2022.Services;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;

namespace AugmentVS2022.Instructions
{
    /// <summary>
    /// Provides inline instruction processing for Augment
    /// </summary>
    [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("text")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    internal class InlineInstructionProvider : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            var processor = new InlineInstructionProcessor(textView);
        }
    }

    /// <summary>
    /// Processes inline instructions in the text editor
    /// </summary>
    internal class InlineInstructionProcessor
    {
        private readonly IWpfTextView _textView;
        private readonly Regex _instructionPattern;
        private IAugmentApiService _apiService;

        public InlineInstructionProcessor(IWpfTextView textView)
        {
            _textView = textView;
            
            // Pattern to match inline instructions: // @augment: instruction text
            _instructionPattern = new Regex(@"^\s*//\s*@augment:\s*(.+)$", RegexOptions.IgnoreCase | RegexOptions.Compiled);
            
            // Subscribe to text changes
            _textView.TextBuffer.Changed += OnTextBufferChanged;
            
            // Get API service
            try
            {
                var package = Microsoft.VisualStudio.Shell.ServiceProvider.GlobalProvider.GetService(typeof(AugmentPackage)) as AugmentPackage;
                _apiService = package?.GetService(typeof(IAugmentApiService)) as IAugmentApiService;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to get API service: {ex.Message}");
            }
        }

        private void OnTextBufferChanged(object sender, TextContentChangedEventArgs e)
        {
            // Check if the change might have completed an instruction
            foreach (var change in e.Changes)
            {
                if (change.NewText.Contains("\n") || change.NewText.Contains("\r"))
                {
                    // A new line was added, check if the previous line contains an instruction
                    var snapshot = e.After;
                    var position = change.NewPosition;
                    
                    if (position > 0)
                    {
                        var line = snapshot.GetLineFromPosition(position - 1);
                        CheckForInstruction(line);
                    }
                }
            }
        }

        private void CheckForInstruction(ITextSnapshotLine line)
        {
            var lineText = line.GetText();
            var match = _instructionPattern.Match(lineText);
            
            if (match.Success)
            {
                var instruction = match.Groups[1].Value.Trim();
                if (!string.IsNullOrEmpty(instruction))
                {
                    // Process the instruction asynchronously
                    Task.Run(async () => await ProcessInlineInstructionAsync(line, instruction));
                }
            }
        }

        private async Task ProcessInlineInstructionAsync(ITextSnapshotLine instructionLine, string instruction)
        {
            if (_apiService == null)
                return;

            try
            {
                // Find the code to modify (look for code above the instruction)
                var codeToModify = ExtractCodeToModify(instructionLine);
                if (string.IsNullOrEmpty(codeToModify))
                    return;

                // Create context
                var context = CreateContext(instructionLine);

                // Execute instruction
                var response = await _apiService.ExecuteInstructionAsync(instruction, codeToModify, context);

                if (response.Success && !string.IsNullOrEmpty(response.ModifiedCode))
                {
                    // Apply changes on UI thread
                    await Microsoft.VisualStudio.Shell.ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    ApplyInlineInstruction(instructionLine, codeToModify, response.ModifiedCode);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Inline instruction error: {ex.Message}");
            }
        }

        private string ExtractCodeToModify(ITextSnapshotLine instructionLine)
        {
            var snapshot = instructionLine.Snapshot;
            var lineNumber = instructionLine.LineNumber;
            
            // Look for code above the instruction line
            var codeLines = new System.Collections.Generic.List<string>();
            var currentLine = lineNumber - 1;
            
            // Go backwards to find the start of the code block
            while (currentLine >= 0)
            {
                var line = snapshot.GetLineFromLineNumber(currentLine);
                var lineText = line.GetText().Trim();
                
                // Stop at empty lines or other instruction comments
                if (string.IsNullOrEmpty(lineText) || _instructionPattern.IsMatch(lineText))
                    break;
                
                codeLines.Insert(0, line.GetText());
                currentLine--;
                
                // Limit to reasonable number of lines
                if (codeLines.Count >= 20)
                    break;
            }
            
            return string.Join("\n", codeLines);
        }

        private AugmentContext CreateContext(ITextSnapshotLine instructionLine)
        {
            var buffer = _textView.TextBuffer;
            
            // Get file path
            string filePath = "untitled";
            if (buffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
            {
                filePath = document.FilePath;
            }

            // Determine language
            var contentType = buffer.ContentType;
            var language = GetLanguageFromContentType(contentType);

            return new AugmentContext
            {
                FilePath = filePath,
                Language = language,
                CursorPosition = instructionLine.Start.Position,
                Metadata = new System.Collections.Generic.Dictionary<string, object>
                {
                    ["instructionLine"] = instructionLine.LineNumber,
                    ["isInlineInstruction"] = true
                }
            };
        }

        private string GetLanguageFromContentType(IContentType contentType)
        {
            if (contentType.IsOfType("CSharp"))
                return "csharp";
            if (contentType.IsOfType("Basic"))
                return "vb";
            if (contentType.IsOfType("C/C++"))
                return "cpp";
            if (contentType.IsOfType("JavaScript"))
                return "javascript";
            if (contentType.IsOfType("TypeScript"))
                return "typescript";
            if (contentType.IsOfType("Python"))
                return "python";
            
            return "text";
        }

        private void ApplyInlineInstruction(ITextSnapshotLine instructionLine, string originalCode, string modifiedCode)
        {
            Microsoft.VisualStudio.Shell.ThreadHelper.ThrowIfNotOnUIThread();

            try
            {
                var buffer = _textView.TextBuffer;
                var snapshot = instructionLine.Snapshot;
                
                using (var edit = buffer.CreateEdit())
                {
                    // Find the span of the original code
                    var lineNumber = instructionLine.LineNumber;
                    var originalLines = originalCode.Split('\n');
                    
                    // Calculate the start position (line above the instruction minus the number of original lines)
                    var startLine = lineNumber - originalLines.Length;
                    if (startLine < 0) startLine = 0;
                    
                    var startPosition = snapshot.GetLineFromLineNumber(startLine).Start.Position;
                    var endPosition = instructionLine.Start.Position;
                    
                    // Replace the original code with modified code
                    var span = new Span(startPosition, endPosition - startPosition);
                    edit.Replace(span, modifiedCode + "\n");
                    
                    // Remove the instruction line
                    edit.Delete(instructionLine.ExtentIncludingLineBreak);
                    
                    edit.Apply();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying inline instruction: {ex.Message}");
            }
        }
    }
}
