using System;
using System.ComponentModel.Design;
using System.Threading;
using System.Threading.Tasks;
using AugmentVS2022.Services;
using AugmentVS2022.UI;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.TextManager.Interop;
using Task = System.Threading.Tasks.Task;

namespace AugmentVS2022.Commands
{
    /// <summary>
    /// Command handler for Augment Instructions
    /// </summary>
    internal sealed class AugmentInstructionsCommand
    {
        /// <summary>
        /// Command ID.
        /// </summary>
        public const int CommandId = 0x0101;

        /// <summary>
        /// Command menu group (command set GUID).
        /// </summary>
        public static readonly Guid CommandSet = new Guid("a7c02a2b-8b4e-4f5d-9e3f-1a2b3c4d5e6f");

        /// <summary>
        /// VS Package that provides this command, not null.
        /// </summary>
        private readonly AsyncPackage package;

        /// <summary>
        /// Initializes a new instance of the <see cref="AugmentInstructionsCommand"/> class.
        /// </summary>
        /// <param name="package">Owner package, not null.</param>
        /// <param name="commandService">Command service to add command to, not null.</param>
        private AugmentInstructionsCommand(AsyncPackage package, OleMenuCommandService commandService)
        {
            this.package = package ?? throw new ArgumentNullException(nameof(package));
            commandService = commandService ?? throw new ArgumentNullException(nameof(commandService));

            var menuCommandID = new CommandID(CommandSet, CommandId);
            var menuItem = new MenuCommand(this.Execute, menuCommandID);
            commandService.AddCommand(menuItem);
        }

        /// <summary>
        /// Gets the instance of the command.
        /// </summary>
        public static AugmentInstructionsCommand Instance
        {
            get;
            private set;
        }

        /// <summary>
        /// Gets the service provider from the owner package.
        /// </summary>
        private Microsoft.VisualStudio.Shell.IAsyncServiceProvider ServiceProvider
        {
            get
            {
                return this.package;
            }
        }

        /// <summary>
        /// Initializes the singleton instance of the command.
        /// </summary>
        /// <param name="package">Owner package, not null.</param>
        public static async Task InitializeAsync(AsyncPackage package)
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync(package.DisposalToken);

            OleMenuCommandService commandService = await package.GetServiceAsync(typeof(IMenuCommandService)) as OleMenuCommandService;
            Instance = new AugmentInstructionsCommand(package, commandService);
        }

        /// <summary>
        /// This function is the callback used to execute the command when the menu item is clicked.
        /// </summary>
        /// <param name="sender">Event sender.</param>
        /// <param name="e">Event args.</param>
        private void Execute(object sender, EventArgs e)
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            try
            {
                // Get the active text view
                var textView = GetActiveTextView();
                if (textView == null)
                {
                    ShowMessage("No active editor found.");
                    return;
                }

                // Get selected text or current line
                var selectedText = GetSelectedTextOrCurrentLine(textView);
                if (string.IsNullOrEmpty(selectedText))
                {
                    ShowMessage("No text selected or current line is empty.");
                    return;
                }

                // Show instruction dialog
                var dialog = new InstructionDialog();
                var result = dialog.ShowModal();

                if (result == true && !string.IsNullOrEmpty(dialog.Instruction))
                {
                    // Execute instruction asynchronously
                    Task.Run(async () => await ExecuteInstructionAsync(textView, selectedText, dialog.Instruction));
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Error: {ex.Message}");
            }
        }

        private async Task ExecuteInstructionAsync(IWpfTextView textView, string selectedText, string instruction)
        {
            try
            {
                // Get API service
                var apiService = package.GetService(typeof(IAugmentApiService)) as IAugmentApiService;
                if (apiService == null)
                {
                    await ShowMessageAsync("Augment API service not available.");
                    return;
                }

                // Create context
                var context = CreateContext(textView);

                // Execute instruction
                var response = await apiService.ExecuteInstructionAsync(instruction, selectedText, context);

                if (response.Success && !string.IsNullOrEmpty(response.ModifiedCode))
                {
                    // Apply changes on UI thread
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    ApplyCodeChanges(textView, response.ModifiedCode, response.Explanation);
                }
                else
                {
                    await ShowMessageAsync($"Instruction failed: {response.Error ?? "Unknown error"}");
                }
            }
            catch (Exception ex)
            {
                await ShowMessageAsync($"Error executing instruction: {ex.Message}");
            }
        }

        private IWpfTextView GetActiveTextView()
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            var textManager = ServiceProvider.GetService(typeof(SVsTextManager)) as IVsTextManager2;
            if (textManager == null)
                return null;

            textManager.GetActiveView2(1, null, (uint)_VIEWFRAMETYPE.vftCodeWindow, out IVsTextView view);
            
            if (view == null)
                return null;

            var userData = view as IVsUserData;
            if (userData == null)
                return null;

            var guidViewHost = Microsoft.VisualStudio.Editor.DefGuidList.guidIWpfTextViewHost;
            userData.GetData(ref guidViewHost, out object holder);
            
            var viewHost = holder as Microsoft.VisualStudio.Text.Editor.IWpfTextViewHost;
            return viewHost?.TextView;
        }

        private string GetSelectedTextOrCurrentLine(IWpfTextView textView)
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            var selection = textView.Selection;
            
            if (!selection.IsEmpty)
            {
                // Return selected text
                return selection.StreamSelectionSpan.GetText();
            }
            else
            {
                // Return current line
                var caretPosition = textView.Caret.Position.BufferPosition;
                var line = caretPosition.GetContainingLine();
                return line.GetText();
            }
        }

        private AugmentContext CreateContext(IWpfTextView textView)
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            var buffer = textView.TextBuffer;
            var caretPosition = textView.Caret.Position.BufferPosition;
            
            // Get file path
            string filePath = "untitled";
            if (buffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
            {
                filePath = document.FilePath;
            }

            // Determine language
            var contentType = buffer.ContentType;
            var language = GetLanguageFromContentType(contentType);

            return new AugmentContext
            {
                FilePath = filePath,
                Language = language,
                CursorPosition = caretPosition.Position,
                ProjectPath = GetProjectPath(filePath)
            };
        }

        private string GetLanguageFromContentType(Microsoft.VisualStudio.Utilities.IContentType contentType)
        {
            if (contentType.IsOfType("CSharp"))
                return "csharp";
            if (contentType.IsOfType("Basic"))
                return "vb";
            if (contentType.IsOfType("C/C++"))
                return "cpp";
            if (contentType.IsOfType("JavaScript"))
                return "javascript";
            if (contentType.IsOfType("TypeScript"))
                return "typescript";
            if (contentType.IsOfType("Python"))
                return "python";
            
            return "text";
        }

        private string GetProjectPath(string filePath)
        {
            // TODO: Get actual project path from Visual Studio
            if (!string.IsNullOrEmpty(filePath))
            {
                var directory = System.IO.Path.GetDirectoryName(filePath);
                return directory ?? "";
            }
            return "";
        }

        private void ApplyCodeChanges(IWpfTextView textView, string modifiedCode, string explanation)
        {
            ThreadHelper.ThrowIfNotOnUIThread();

            try
            {
                var selection = textView.Selection;
                var buffer = textView.TextBuffer;

                using (var edit = buffer.CreateEdit())
                {
                    if (!selection.IsEmpty)
                    {
                        // Replace selected text
                        var span = selection.StreamSelectionSpan.SnapshotSpan;
                        edit.Replace(span, modifiedCode);
                    }
                    else
                    {
                        // Replace current line
                        var caretPosition = textView.Caret.Position.BufferPosition;
                        var line = caretPosition.GetContainingLine();
                        edit.Replace(line.Extent, modifiedCode);
                    }

                    edit.Apply();
                }

                // Show explanation if available
                if (!string.IsNullOrEmpty(explanation))
                {
                    ShowMessage($"Applied instruction: {explanation}");
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Error applying changes: {ex.Message}");
            }
        }

        private void ShowMessage(string message)
        {
            ThreadHelper.ThrowIfNotOnUIThread();
            
            Microsoft.VisualStudio.Shell.VsShellUtilities.ShowMessageBox(
                this.package,
                message,
                "Augment Instructions",
                Microsoft.VisualStudio.Shell.Interop.OLEMSGICON.OLEMSGICON_INFO,
                Microsoft.VisualStudio.Shell.Interop.OLEMSGBUTTON.OLEMSGBUTTON_OK,
                Microsoft.VisualStudio.Shell.Interop.OLEMSGDEFBUTTON.OLEMSGDEFBUTTON_FIRST);
        }

        private async Task ShowMessageAsync(string message)
        {
            await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
            ShowMessage(message);
        }
    }
}
