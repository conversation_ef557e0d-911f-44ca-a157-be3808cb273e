{"RootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio", "ProjectFileName": "AugmentVS2022.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AugmentPackage.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Services\\IAugmentAuthService.cs"}, {"SourceFile": "Services\\AugmentAuthService.cs"}, {"SourceFile": "Services\\IAugmentApiService.cs"}, {"SourceFile": "Services\\AugmentApiService.cs"}, {"SourceFile": "Services\\IAugmentCompletionService.cs"}, {"SourceFile": "Services\\AugmentCompletionService.cs"}, {"SourceFile": "UI\\ChatToolWindow.cs"}, {"SourceFile": "UI\\ChatToolWindowControl.xaml.cs"}, {"SourceFile": "Commands\\ShowChatWindowCommand.cs"}, {"SourceFile": "Commands\\AugmentInstructionsCommand.cs"}, {"SourceFile": "Completions\\AugmentCompletionProvider.cs"}, {"SourceFile": "Instructions\\InlineInstructionProvider.cs"}, {"SourceFile": "UI\\InstructionDialog.xaml.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\obj\\Debug\\UI\\ChatToolWindowControl.g.cs"}, {"SourceFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\obj\\Debug\\UI\\InstructionDialog.g.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\envdte\\17.0.32112.339\\lib\\net472\\envdte.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\envdte100\\17.0.32112.339\\lib\\net472\\envdte100.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\envdte80\\17.0.32112.339\\lib\\net472\\envdte80.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\envdte90\\17.0.32112.339\\lib\\net472\\envdte90.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\envdte90a\\17.0.32112.339\\lib\\net472\\envdte90a.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\messagepack.annotations\\2.2.85\\lib\\netstandard2.0\\MessagePack.Annotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\messagepack\\2.2.85\\lib\\netstandard2.0\\MessagePack.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\5.0.0\\lib\\net461\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\16.5.0\\lib\\net472\\Microsoft.Build.Framework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.client\\3.0.3078\\lib\\net472\\Microsoft.ServiceHub.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.framework\\3.0.3078\\lib\\netstandard2.0\\Microsoft.ServiceHub.Framework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.servicehub.resources\\3.0.3078\\lib\\net472\\Microsoft.ServiceHub.Resources.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.commandbars\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.CommandBars.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.componentmodelhost\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.ComponentModelHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.coreutility\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.CoreUtility.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.10.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.10.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.11.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.11.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.12.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.12.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.14.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.14.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.15.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.15.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interop.16.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.Interop.16.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.debugger.interopa\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Debugger.InteropA.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.designer.interfaces\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Designer.Interfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.editor\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Editor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.graphmodel\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.GraphModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imagecatalog\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.ImageCatalog.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imaging\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Imaging.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.imaging.interop.14.0.designtime\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Imaging.Interop.14.0.DesignTime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.interop\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Language.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.intellisense\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Language.Intellisense.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.navigateto.interfaces\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Language.NavigateTo.Interfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.language.standardclassification\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Language.StandardClassification.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.languageserver.client\\17.0.5165\\lib\\net472\\Microsoft.VisualStudio.LanguageServer.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.ole.interop\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.OLE.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.package.languageservice.15.0\\17.0.32112.339\\lib\\net45\\Microsoft.VisualStudio.Package.LanguageService.15.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.projectaggregator\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.ProjectAggregator.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.remotecontrol\\16.3.41\\lib\\net45\\Microsoft.VisualStudio.RemoteControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.rpccontracts\\17.0.51\\lib\\netstandard2.0\\Microsoft.VisualStudio.RpcContracts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.setup.configuration.interop\\3.0.4496\\lib\\net35\\Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.15.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.15.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.design\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.framework\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Framework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.10.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.10.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.11.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.11.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.12.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.12.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.8.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.8.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop.9.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.9.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.shell.interop\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Shell.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.taskrunnerexplorer.14.0\\14.0.0\\lib\\net40\\Microsoft.VisualStudio.TaskRunnerExplorer.14.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.telemetry\\16.3.250\\lib\\net45\\Microsoft.VisualStudio.Telemetry.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.data\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Text.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.logic\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Text.Logic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.ui\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Text.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.text.ui.wpf\\17.0.491\\lib\\net472\\Microsoft.VisualStudio.Text.UI.Wpf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.10.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.10.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.11.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.11.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.12.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.12.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.8.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.8.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop.9.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.9.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.textmanager.interop\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextManager.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextTemplating.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces.10.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextTemplating.Interfaces.10.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces.11.0\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextTemplating.Interfaces.11.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.interfaces\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextTemplating.Interfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.texttemplating.vshost\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.TextTemplating.VSHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading\\17.0.64\\lib\\net472\\Microsoft.VisualStudio.Threading.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.utilities\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.0.28\\lib\\netstandard2.0\\Microsoft.VisualStudio.Validation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vcprojectengine\\17.0.32112.339\\lib\\net45\\Microsoft.VisualStudio.VCProjectEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vshelp\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.VSHelp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.vshelp80\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.VSHelp80.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.wcfreference.interop\\17.0.32112.339\\lib\\net472\\Microsoft.VisualStudio.WCFReference.Interop.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.web.browserlink.12.0\\12.0.0\\lib\\net40\\Microsoft.VisualStudio.Web.BrowserLink.12.0.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\Microsoft.Win32.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\ref\\net461\\Microsoft.Win32.Registry.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\nerdbank.streams\\2.6.81\\lib\\netstandard2.0\\Nerdbank.Streams.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\stdole\\17.0.32112.339\\lib\\net472\\stdole.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\streamjsonrpc\\2.8.28\\lib\\netstandard2.0\\StreamJsonRpc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\ref\\net45\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\5.0.0\\lib\\net461\\System.Collections.Immutable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\5.0.1\\lib\\net46\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\5.0.1\\lib\\net461\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Net.WebSockets.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\ref\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\ref\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\ref\\net461\\System.Security.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\ref\\net461\\System.Security.Principal.Windows.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\5.0.0\\ref\\net461\\System.Threading.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.dataflow\\5.0.0\\lib\\net461\\System.Threading.Tasks.Dataflow.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj\\17.0.32112.339\\lib\\net472\\VSLangProj.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj100\\17.0.32112.339\\lib\\net472\\VSLangProj100.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj110\\17.0.32112.339\\lib\\net472\\VSLangProj110.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj140\\17.0.32112.339\\lib\\net472\\VSLangProj140.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj150\\17.0.32112.339\\lib\\net472\\VSLangProj150.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj157\\17.0.32112.339\\lib\\net472\\VSLangProj157.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj158\\17.0.32112.339\\lib\\net472\\VSLangProj158.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj165\\17.0.32112.339\\lib\\net472\\VSLangProj165.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj2\\17.0.32112.339\\lib\\net472\\VSLangProj2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj80\\17.0.32112.339\\lib\\net472\\VSLangProj80.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\.nuget\\packages\\vslangproj90\\17.0.32112.339\\lib\\net472\\VSLangProj90.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Augment for Visual Studio\\bin\\Debug\\AugmentVS2022.dll", "OutputItemRelativePath": "AugmentVS2022.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}