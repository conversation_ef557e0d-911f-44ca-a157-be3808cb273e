<UserControl x:Class="AugmentVS2022.UI.ChatToolWindowControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:vsshell="clr-namespace:Microsoft.VisualStudio.Shell;assembly=Microsoft.VisualStudio.Shell.15.0"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             Name="AugmentChatControl">
    <UserControl.Resources>
        <Style x:Key="ChatMessageStyle" TargetType="Border">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Background" Value="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"/>
        </Style>
        
        <Style x:Key="UserMessageStyle" TargetType="Border" BasedOn="{StaticResource ChatMessageStyle}">
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Background" Value="{DynamicResource {x:Static vsshell:VsBrushes.AccentLightKey}}"/>
            <Setter Property="MaxWidth" Value="300"/>
        </Style>
        
        <Style x:Key="AiMessageStyle" TargetType="Border" BasedOn="{StaticResource ChatMessageStyle}">
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Background" Value="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="MaxWidth" Value="300"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource {x:Static vsshell:VsBrushes.AccentDarkKey}}" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="Augment Chat" 
                          FontWeight="Bold" 
                          Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                          VerticalAlignment="Center"/>
                <Button Name="LoginButton" 
                        Content="Login" 
                        Margin="10,0,0,0" 
                        Padding="8,4"
                        Click="LoginButton_Click"
                        Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=Inverse}"/>
                <Button Name="LogoutButton" 
                        Content="Logout" 
                        Margin="10,0,0,0" 
                        Padding="8,4"
                        Click="LogoutButton_Click"
                        Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StackPanel>
        </Border>
        
        <!-- Chat Messages -->
        <ScrollViewer Grid.Row="1" 
                      Name="ChatScrollViewer"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="5">
            <ItemsControl Name="ChatMessages" ItemsSource="{Binding Messages}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{Binding IsUser, Converter={StaticResource MessageStyleConverter}}">
                            <StackPanel>
                                <TextBlock Text="{Binding Sender}" 
                                          FontWeight="Bold" 
                                          FontSize="12"
                                          Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                                          Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding Content}" 
                                          TextWrapping="Wrap"
                                          Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"/>
                                <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:HH:mm}'}" 
                                          FontSize="10"
                                          Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                                          Opacity="0.7"
                                          HorizontalAlignment="Right"
                                          Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                BorderThickness="0,1,0,0"
                Padding="10,5">
            <TextBlock Name="StatusText" 
                      Text="{Binding StatusMessage}"
                      Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                      FontSize="12"/>
        </Border>
        
        <!-- Input Area -->
        <Border Grid.Row="3" 
                Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"
                BorderThickness="0,1,0,0"
                Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Name="MessageInput" 
                         Grid.Column="0"
                         Text="{Binding CurrentMessage, UpdateSourceTrigger=PropertyChanged}"
                         AcceptsReturn="True"
                         MaxHeight="100"
                         VerticalScrollBarVisibility="Auto"
                         TextWrapping="Wrap"
                         KeyDown="MessageInput_KeyDown"
                         IsEnabled="{Binding IsLoggedIn}"
                         Background="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBackgroundKey}}"
                         Foreground="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowTextKey}}"
                         BorderBrush="{DynamicResource {x:Static vsshell:VsBrushes.ToolWindowBorderKey}}"/>
                
                <Button Name="SendButton" 
                        Grid.Column="1"
                        Content="Send"
                        Margin="10,0,0,0"
                        Padding="15,8"
                        Click="SendButton_Click"
                        IsEnabled="{Binding CanSendMessage}"
                        IsDefault="True"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
