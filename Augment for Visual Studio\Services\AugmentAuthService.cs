using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.VisualStudio.Settings;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Shell.Settings;
using Newtonsoft.Json;

namespace Augment_for_Visual_Studio.Services
{
    /// <summary>
    /// Implementation of the Augment authentication service
    /// </summary>
    public class AugmentAuthService : IAugmentAuthService
    {
        private const string SETTINGS_COLLECTION = "AugmentVS2022";
        private const string ACCESS_TOKEN_KEY = "AccessToken";
        private const string REFRESH_TOKEN_KEY = "RefreshToken";
        private const string USER_INFO_KEY = "UserInfo";
        
        private const string AUTH_URL = "https://api.augmentcode.com/auth";
        private const string TOKEN_URL = "https://api.augmentcode.com/token";
        private const string USER_URL = "https://api.augmentcode.com/user";

        private readonly HttpClient _httpClient;
        private WritableSettingsStore _settingsStore;
        
        private string _accessToken;
        private string _refreshToken;
        private AugmentUser _currentUser;

        public bool IsAuthenticated => !string.IsNullOrEmpty(_accessToken) && _currentUser != null;
        public string AccessToken => _accessToken;
        public AugmentUser CurrentUser => _currentUser;

        public event EventHandler<AuthenticationChangedEventArgs> AuthenticationChanged;

        public AugmentAuthService()
        {
            _httpClient = new HttpClient();
            
            // Initialize settings store (will be done when package is loaded)
            InitializeSettingsStore();
            
            // Load saved tokens
            LoadSavedCredentials();
        }

        private void InitializeSettingsStore()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                var settingsManager = new ShellSettingsManager(ServiceProvider.GlobalProvider);
                _settingsStore = settingsManager.GetWritableSettingsStore(SettingsScope.UserSettings);
                
                if (!_settingsStore.CollectionExists(SETTINGS_COLLECTION))
                {
                    _settingsStore.CreateCollection(SETTINGS_COLLECTION);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to initialize settings store: {ex.Message}");
            }
        }

        public async Task<bool> LoginAsync()
        {
            try
            {
                // For demo purposes, simulate a successful login
                // In a real implementation, this would open a browser for OAuth flow
                
                _accessToken = "demo_access_token_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                _refreshToken = "demo_refresh_token_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                
                // Simulate getting user info
                _currentUser = new AugmentUser 
                { 
                    Id = "demo_user_123", 
                    Email = "<EMAIL>", 
                    Name = "Demo User",
                    Plan = "Professional"
                };
                
                // Save credentials
                SaveCredentials();
                
                // Fire event
                AuthenticationChanged?.Invoke(this, new AuthenticationChangedEventArgs
                {
                    IsAuthenticated = true,
                    User = _currentUser,
                    Reason = "Login successful"
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Login failed: {ex.Message}");
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            _accessToken = null;
            _refreshToken = null;
            _currentUser = null;
            
            // Clear saved credentials
            ClearSavedCredentials();
            
            // Fire event
            AuthenticationChanged?.Invoke(this, new AuthenticationChangedEventArgs
            {
                IsAuthenticated = false,
                User = null,
                Reason = "User logged out"
            });
            
            await Task.CompletedTask;
        }

        public async Task<bool> RefreshTokenAsync()
        {
            if (string.IsNullOrEmpty(_refreshToken))
            {
                return false;
            }

            try
            {
                // Simulate token refresh
                _accessToken = "refreshed_token_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                SaveCredentials();
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Token refresh failed: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ValidateTokenAsync()
        {
            if (string.IsNullOrEmpty(_accessToken))
            {
                return false;
            }

            try
            {
                // Simulate token validation
                await Task.Delay(100);
                return !string.IsNullOrEmpty(_accessToken);
            }
            catch
            {
                return false;
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                if (_settingsStore?.PropertyExists(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY) == true)
                {
                    _accessToken = _settingsStore.GetString(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY);
                }
                
                if (_settingsStore?.PropertyExists(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY) == true)
                {
                    _refreshToken = _settingsStore.GetString(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY);
                }
                
                if (_settingsStore?.PropertyExists(SETTINGS_COLLECTION, USER_INFO_KEY) == true)
                {
                    var userJson = _settingsStore.GetString(SETTINGS_COLLECTION, USER_INFO_KEY);
                    _currentUser = JsonConvert.DeserializeObject<AugmentUser>(userJson);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to load saved credentials: {ex.Message}");
            }
        }

        private void SaveCredentials()
        {
            try
            {
                if (_settingsStore == null) return;
                
                if (!string.IsNullOrEmpty(_accessToken))
                {
                    _settingsStore.SetString(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY, _accessToken);
                }
                
                if (!string.IsNullOrEmpty(_refreshToken))
                {
                    _settingsStore.SetString(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY, _refreshToken);
                }
                
                if (_currentUser != null)
                {
                    var userJson = JsonConvert.SerializeObject(_currentUser);
                    _settingsStore.SetString(SETTINGS_COLLECTION, USER_INFO_KEY, userJson);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to save credentials: {ex.Message}");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                if (_settingsStore == null) return;
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, ACCESS_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, REFRESH_TOKEN_KEY);
                }
                
                if (_settingsStore.PropertyExists(SETTINGS_COLLECTION, USER_INFO_KEY))
                {
                    _settingsStore.DeleteProperty(SETTINGS_COLLECTION, USER_INFO_KEY);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to clear saved credentials: {ex.Message}");
            }
        }
    }
}
